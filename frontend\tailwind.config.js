/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class', // 启用类名模式的深色主题
  theme: {
    extend: {
      colors: {
        // 传统变量（兼容现有代码）
        background: 'var(--background)',
        foreground: 'var(--foreground)',

        // 新的主题变量系统
        'theme-background': 'var(--color-background)',
        'theme-background-secondary': 'var(--color-background-secondary)',
        'theme-background-tertiary': 'var(--color-background-tertiary)',

        'theme-foreground': 'var(--color-foreground)',
        'theme-foreground-secondary': 'var(--color-foreground-secondary)',
        'theme-foreground-muted': 'var(--color-foreground-muted)',

        'theme-border': 'var(--color-border)',
        'theme-border-secondary': 'var(--color-border-secondary)',

        'theme-card': 'var(--color-card)',
        'theme-card-hover': 'var(--color-card-hover)',

        'theme-input': 'var(--color-input)',
        'theme-input-border': 'var(--color-input-border)',
        'theme-input-focus': 'var(--color-input-focus)',

        'theme-primary': 'var(--color-primary)',
        'theme-primary-hover': 'var(--color-primary-hover)',
        'theme-secondary': 'var(--color-secondary)',
        'theme-secondary-hover': 'var(--color-secondary-hover)',

        'theme-success': 'var(--color-success)',
        'theme-warning': 'var(--color-warning)',
        'theme-error': 'var(--color-error)',
        'theme-info': 'var(--color-info)',

        'theme-accent': 'var(--color-accent)',
        'theme-accent-hover': 'var(--color-accent-hover)',
      },
      animation: {
        'theme-transition': 'theme-transition 0.3s ease-in-out',
      },
      keyframes: {
        'theme-transition': {
          '0%': { opacity: '0.8' },
          '100%': { opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}