/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/mcp-config/page";
exports.ids = ["app/mcp-config/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmcp-config%2Fpage&page=%2Fmcp-config%2Fpage&appPaths=%2Fmcp-config%2Fpage&pagePath=private-next-app-dir%2Fmcp-config%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmcp-config%2Fpage&page=%2Fmcp-config%2Fpage&appPaths=%2Fmcp-config%2Fpage&pagePath=private-next-app-dir%2Fmcp-config%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mcp-config/page.tsx */ \"(rsc)/./src/app/mcp-config/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'mcp-config',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/mcp-config/page\",\n        pathname: \"/mcp-config\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmcp-config%2Fpage&page=%2Fmcp-config%2Fpage&appPaths=%2Fmcp-config%2Fpage&pagePath=private-next-app-dir%2Fmcp-config%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(rsc)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDWmFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNSUDMwX2t1bmFnZW50JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb250ZXh0cyU1QyU1Q1RoZW1lQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBMEoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxaYWNrXFxcXERlc2t0b3BcXFxcUlAzMF9rdW5hZ2VudFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29udGV4dHNcXFxcVGhlbWVDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cmcp-config%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cmcp-config%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mcp-config/page.tsx */ \"(rsc)/./src/app/mcp-config/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbWNwLWNvbmZpZyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBc0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFphY2tcXFxcRGVza3RvcFxcXFxSUDMwX2t1bmFnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbWNwLWNvbmZpZ1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cmcp-config%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d01830fe10d7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkMDE4MzBmZTEwZDdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _components_ThemeScript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ThemeScript */ \"(rsc)/./src/components/ThemeScript.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Kun Agent',\n    description: '与本地Ollama模型进行对话的应用'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeScript__WEBPACK_IMPORTED_MODULE_3__.ThemeScript, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-theme-background-secondary text-theme-foreground transition-colors duration-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDc0I7QUFDaUM7QUFDRDtBQUUvQyxNQUFNRSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQVFDLHdCQUF3Qjs7MEJBQ3pDLDhEQUFDQzswQkFDQyw0RUFBQ1QsZ0VBQVdBOzs7Ozs7Ozs7OzBCQUVkLDhEQUFDVTtnQkFBS0MsV0FBVTswQkFDZCw0RUFBQ1osaUVBQWFBOzhCQUNYTTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9UaGVtZUNvbnRleHQnXG5pbXBvcnQgeyBUaGVtZVNjcmlwdCB9IGZyb20gJ0AvY29tcG9uZW50cy9UaGVtZVNjcmlwdCdcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdLdW4gQWdlbnQnLFxuICBkZXNjcmlwdGlvbjogJ+S4juacrOWcsE9sbGFtYeaooeWei+i/m+ihjOWvueivneeahOW6lOeUqCcsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxoZWFkPlxuICAgICAgICA8VGhlbWVTY3JpcHQgLz5cbiAgICAgIDwvaGVhZD5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy10aGVtZS1iYWNrZ3JvdW5kLXNlY29uZGFyeSB0ZXh0LXRoZW1lLWZvcmVncm91bmQgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCI+XG4gICAgICAgIDxUaGVtZVByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9UaGVtZVByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufSJdLCJuYW1lcyI6WyJUaGVtZVByb3ZpZGVyIiwiVGhlbWVTY3JpcHQiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsInN1cHByZXNzSHlkcmF0aW9uV2FybmluZyIsImhlYWQiLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/mcp-config/page.tsx":
/*!*************************************!*\
  !*** ./src/app/mcp-config/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ThemeScript.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeScript.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeScript: () => (/* binding */ ThemeScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n// 主题预加载脚本组件\n// 这个组件会在页面加载前立即执行，防止主题闪烁\n\nfunction ThemeScript() {\n    const themeScript = `\n    (function() {\n      try {\n        // 获取保存的主题或系统主题\n        const savedTheme = localStorage.getItem('theme');\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        const theme = savedTheme || systemTheme;\n        \n        // 立即应用主题类到html元素\n        document.documentElement.classList.remove('light', 'dark');\n        document.documentElement.classList.add(theme);\n        \n        // 定义主题颜色\n        const themeColors = {\n          light: {\n            background: '#ffffff',\n            backgroundSecondary: '#f8fafc',\n            backgroundTertiary: '#f1f5f9',\n            foreground: '#0f172a',\n            foregroundSecondary: '#334155',\n            foregroundMuted: '#64748b',\n            border: '#e2e8f0',\n            borderSecondary: '#cbd5e1',\n            card: '#ffffff',\n            cardHover: '#f8fafc',\n            input: '#ffffff',\n            inputBorder: '#d1d5db',\n            inputFocus: '#3b82f6',\n            primary: '#3b82f6',\n            primaryHover: '#2563eb',\n            secondary: '#6b7280',\n            secondaryHover: '#4b5563',\n            success: '#10b981',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#3b82f6',\n            accent: '#8b5cf6',\n            accentHover: '#7c3aed',\n          },\n          dark: {\n            background: '#0f172a',\n            backgroundSecondary: '#1e293b',\n            backgroundTertiary: '#334155',\n            foreground: '#f8fafc',\n            foregroundSecondary: '#e2e8f0',\n            foregroundMuted: '#94a3b8',\n            border: '#334155',\n            borderSecondary: '#475569',\n            card: '#1e293b',\n            cardHover: '#334155',\n            input: '#1e293b',\n            inputBorder: '#475569',\n            inputFocus: '#3b82f6',\n            primary: '#3b82f6',\n            primaryHover: '#2563eb',\n            secondary: '#6b7280',\n            secondaryHover: '#9ca3af',\n            success: '#10b981',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#3b82f6',\n            accent: '#8b5cf6',\n            accentHover: '#7c3aed',\n          }\n        };\n        \n        // 设置CSS变量\n        const colors = themeColors[theme];\n        const root = document.documentElement;\n\n        Object.entries(colors).forEach(([key, value]) => {\n          root.style.setProperty('--color-' + key, value);\n        });\n        \n        // 如果没有保存的主题，保存当前主题\n        if (!savedTheme) {\n          localStorage.setItem('theme', theme);\n        }\n      } catch (e) {\n        // 如果出错，使用默认浅色主题\n        document.documentElement.classList.add('light');\n      }\n    })();\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: themeScript\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\components\\\\ThemeScript.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ThemeScript.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme),
/* harmony export */   useThemeToggle: () => (/* binding */ useThemeToggle)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\contexts\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\contexts\\ThemeContext.tsx",
"useTheme",
);const useThemeToggle = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useThemeToggle() from the server but useThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\contexts\\ThemeContext.tsx",
"useThemeToggle",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(ssr)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDWmFjayU1QyU1Q0Rlc2t0b3AlNUMlNUNSUDMwX2t1bmFnZW50JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb250ZXh0cyU1QyU1Q1RoZW1lQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBMEoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxaYWNrXFxcXERlc2t0b3BcXFxcUlAzMF9rdW5hZ2VudFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29udGV4dHNcXFxcVGhlbWVDb250ZXh0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cmcp-config%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cmcp-config%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/mcp-config/page.tsx */ \"(ssr)/./src/app/mcp-config/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1phY2slNUMlNUNEZXNrdG9wJTVDJTVDUlAzMF9rdW5hZ2VudCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbWNwLWNvbmZpZyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBc0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFphY2tcXFxcRGVza3RvcFxcXFxSUDMwX2t1bmFnZW50XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbWNwLWNvbmZpZ1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CZack%5C%5CDesktop%5C%5CRP30_kunagent%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cmcp-config%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/mcp-config/components/AddServerModal.tsx":
/*!**********************************************************!*\
  !*** ./src/app/mcp-config/components/AddServerModal.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddServerModal: () => (/* binding */ AddServerModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Settings,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Settings,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Settings,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ AddServerModal auto */ \n\n\nfunction AddServerModal({ isOpen, newServer, onServerChange, onSubmit, onClose }) {\n    const [validationResult, setValidationResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isValidating, setIsValidating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showToolConfig, setShowToolConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toolConfigs, setToolConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [cachedServerKey, setCachedServerKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(''); // 缓存的服务器标识\n    const [toolsCache, setToolsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 工具缓存\n    // 生成服务器唯一标识\n    const getServerKey = (server)=>{\n        if (server.type === 'stdio') {\n            return `${server.type}-${server.command}-${server.args}`;\n        } else {\n            return `${server.type}-${server.url || server.base_url}`;\n        }\n    };\n    // 监听服务器配置变更，清除不匹配的缓存\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddServerModal.useEffect\": ()=>{\n            const currentKey = getServerKey(newServer);\n            if (cachedServerKey && cachedServerKey !== currentKey) {\n                // 服务器配置已变更，清除缓存\n                setToolsCache(null);\n                setValidationResult(null);\n                setCachedServerKey('');\n                setToolConfigs({});\n            }\n        }\n    }[\"AddServerModal.useEffect\"], [\n        newServer.type,\n        newServer.url,\n        newServer.base_url,\n        newServer.command,\n        newServer.args\n    ]);\n    // 监听模态框关闭，清除缓存\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddServerModal.useEffect\": ()=>{\n            if (!isOpen) {\n                setToolsCache(null);\n                setValidationResult(null);\n                setCachedServerKey('');\n                setToolConfigs({});\n            }\n        }\n    }[\"AddServerModal.useEffect\"], [\n        isOpen\n    ]);\n    // 验证服务器连接\n    const validateServer = async ()=>{\n        if (newServer.type === 'stdio') {\n            if (!newServer.command) {\n                alert('请先填写STDIO命令');\n                return;\n            }\n        } else {\n            if (!newServer.url) {\n                alert('请先填写服务器URL');\n                return;\n            }\n        }\n        const currentKey = getServerKey(newServer);\n        // 检查是否有缓存的结果\n        if (toolsCache && cachedServerKey === currentKey) {\n            setValidationResult(toolsCache);\n            return;\n        }\n        setIsValidating(true);\n        setValidationResult(null);\n        try {\n            const response = await fetch('/api/mcp/validate', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newServer)\n            });\n            if (!response.ok) {\n                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);\n            }\n            const result = await response.json();\n            setValidationResult(result);\n            // 缓存验证结果\n            if (result.success) {\n                setToolsCache(result);\n                setCachedServerKey(currentKey);\n            }\n            if (result.success && result.tools) {\n                // 初始化工具配置\n                const configs = {};\n                result.tools.forEach((tool)=>{\n                    if (tool.inputSchema && tool.inputSchema.properties) {\n                        const defaultConfig = {};\n                        Object.entries(tool.inputSchema.properties).forEach(([key, prop])=>{\n                            if (prop.default !== undefined) {\n                                defaultConfig[key] = prop.default;\n                            }\n                        });\n                        configs[tool.name] = defaultConfig;\n                    }\n                });\n                setToolConfigs(configs);\n            }\n        } catch (error) {\n            const errorResult = {\n                success: false,\n                error: error instanceof Error ? error.message : '连接测试失败'\n            };\n            setValidationResult(errorResult);\n        } finally{\n            setIsValidating(false);\n        }\n    };\n    // 处理添加服务器\n    const handleAdd = async ()=>{\n        const currentKey = getServerKey(newServer);\n        let resultToUse = validationResult;\n        // 如果没有验证结果但有缓存，使用缓存\n        if (!resultToUse && toolsCache && cachedServerKey === currentKey) {\n            resultToUse = toolsCache;\n            setValidationResult(toolsCache);\n        }\n        if (!resultToUse?.success) {\n            alert('请先测试服务器连接');\n            return;\n        }\n        // 将工具配置和验证时获取的工具信息添加到服务器配置中\n        const serverWithConfig = {\n            ...newServer,\n            toolConfigs: toolConfigs,\n            validatedTools: resultToUse.tools // 传递验证时获取的工具信息\n        };\n        console.log('AddServerModal: 准备提交的服务器配置:', JSON.stringify(serverWithConfig, null, 2));\n        // 直接传递完整的服务器配置给onSubmit\n        await onSubmit(serverWithConfig);\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                    children: \"添加MCP服务器\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"服务器名称\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: newServer.name,\n                                    onChange: (e)=>onServerChange({\n                                            ...newServer,\n                                            name: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    placeholder: \"例如: my-server\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"服务器类型\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: newServer.type,\n                                    onChange: (e)=>{\n                                        const type = e.target.value;\n                                        onServerChange({\n                                            ...newServer,\n                                            type\n                                        });\n                                    },\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"stdio\",\n                                            children: \"STDIO (本地进程)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"sse\",\n                                            children: \"SSE (Server-Sent Events)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"streamable-http\",\n                                            children: \"Streamable HTTP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        newServer.type === 'stdio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"命令\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newServer.command || '',\n                                            onChange: (e)=>onServerChange({\n                                                    ...newServer,\n                                                    command: e.target.value\n                                                }),\n                                            className: \"flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            placeholder: \"例如: python\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: validateServer,\n                                            disabled: isValidating || !newServer.command,\n                                            className: `px-3 py-1 text-white rounded text-sm flex items-center gap-1 ${toolsCache && cachedServerKey === getServerKey(newServer) ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} disabled:bg-gray-400 disabled:cursor-not-allowed`,\n                                            children: isValidating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"测试中...\"\n                                                ]\n                                            }, void 0, true) : toolsCache && cachedServerKey === getServerKey(newServer) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"已验证\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"测试连接\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                            children: \"参数 (可选)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: newServer.args ? JSON.parse(newServer.args).join(' ') : '',\n                                            onChange: (e)=>onServerChange({\n                                                    ...newServer,\n                                                    args: JSON.stringify(e.target.value.split(' ').filter((arg)=>arg.trim()))\n                                                }),\n                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            placeholder: \"例如: script.py --verbose\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this),\n                        (newServer.type === 'sse' || newServer.type === 'streamable-http') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"服务器URL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"url\",\n                                            value: newServer.url || '',\n                                            onChange: (e)=>onServerChange({\n                                                    ...newServer,\n                                                    url: e.target.value\n                                                }),\n                                            className: \"flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                            placeholder: newServer.type === 'sse' ? \"例如: http://localhost:8080/sse\" : \"例如: http://localhost:8080/mcp\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: validateServer,\n                                            disabled: isValidating || (newServer.type === 'sse' || newServer.type === 'streamable-http') && !newServer.url,\n                                            className: `px-3 py-1 text-white rounded text-sm flex items-center gap-1 ${toolsCache && cachedServerKey === getServerKey(newServer) ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'} disabled:bg-gray-400 disabled:cursor-not-allowed`,\n                                            children: isValidating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"连接中...\"\n                                                ]\n                                            }, void 0, true) : toolsCache && cachedServerKey === getServerKey(newServer) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"已验证\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"测试连接\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this),\n                        validationResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `p-3 rounded-md ${validationResult.success ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        validationResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `text-sm font-medium ${validationResult.success ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'}`,\n                                            children: validationResult.success ? validationResult.message : validationResult.error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this),\n                                validationResult.success && validationResult.toolCount !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 dark:text-green-300\",\n                                            children: [\n                                                \"发现 \",\n                                                validationResult.toolCount,\n                                                \" 个可用工具\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 19\n                                        }, this),\n                                        validationResult.toolCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowToolConfig(!showToolConfig),\n                                            className: \"mt-2 flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400 hover:underline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Settings_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 23\n                                                }, this),\n                                                showToolConfig ? '隐藏' : '配置',\n                                                \"工具参数\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, this),\n                        showToolConfig && validationResult?.success && validationResult.tools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 dark:border-gray-700 rounded-md p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-3\",\n                                    children: \"工具参数配置\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 max-h-60 overflow-y-auto\",\n                                    children: validationResult.tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-100 dark:border-gray-600 rounded p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white text-sm\",\n                                                    children: tool.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400 mb-2\",\n                                                    children: tool.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 21\n                                                }, this),\n                                                tool.inputSchema?.properties && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: Object.entries(tool.inputSchema.properties).map(([key, prop])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                                    children: [\n                                                                        key,\n                                                                        \" \",\n                                                                        prop.description && `(${prop.description})`\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                                    lineNumber: 387,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: prop.type === 'number' ? 'number' : 'text',\n                                                                    value: toolConfigs[tool.name]?.[key] || prop.default || '',\n                                                                    onChange: (e)=>{\n                                                                        const value = prop.type === 'number' ? Number(e.target.value) : e.target.value;\n                                                                        setToolConfigs((prev)=>({\n                                                                                ...prev,\n                                                                                [tool.name]: {\n                                                                                    ...prev[tool.name],\n                                                                                    [key]: value\n                                                                                }\n                                                                            }));\n                                                                    },\n                                                                    className: \"w-full p-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                                                    placeholder: prop.default?.toString() || ''\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, tool.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                    children: \"描述 (可选)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: newServer.description || '',\n                                    onChange: (e)=>onServerChange({\n                                            ...newServer,\n                                            description: e.target.value\n                                        }),\n                                    className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                    rows: 2,\n                                    placeholder: \"服务器描述...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onClose();\n                                // 清理所有状态\n                                setValidationResult(null);\n                                setShowToolConfig(false);\n                                setToolConfigs({});\n                                setToolsCache(null);\n                                setCachedServerKey('');\n                            },\n                            className: \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleAdd,\n                            disabled: (()=>{\n                                const currentKey = getServerKey(newServer);\n                                const hasValidResult = validationResult?.success;\n                                const hasCachedResult = toolsCache?.success && cachedServerKey === currentKey;\n                                return !(hasValidResult || hasCachedResult);\n                            })(),\n                            className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: \"添加\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n                    lineNumber: 430,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\AddServerModal.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mcp-config/components/AddServerModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/mcp-config/components/PageHeader.tsx":
/*!******************************************************!*\
  !*** ./src/app/mcp-config/components/PageHeader.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageHeader: () => (/* binding */ PageHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Plus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction PageHeader({ onAddServer }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-card border-b border-theme-border transition-colors duration-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"p-2 text-theme-foreground-muted hover:text-theme-foreground transition-colors duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-theme-foreground\",\n                                children: \"MCP 服务器配置\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onAddServer,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover transition-colors duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, this),\n                                \"添加服务器\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\PageHeader.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mcp-config/components/PageHeader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/mcp-config/components/ToolsModal.tsx":
/*!******************************************************!*\
  !*** ./src/app/mcp-config/components/ToolsModal.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolsModal: () => (/* binding */ ToolsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Play,Power,PowerOff,Settings,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Play,Power,PowerOff,Settings,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Play,Power,PowerOff,Settings,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Play,Power,PowerOff,Settings,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Play,Power,PowerOff,Settings,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Play,Power,PowerOff,Settings,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Play,Power,PowerOff,Settings,Wrench,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* __next_internal_client_entry_do_not_use__ ToolsModal auto */ \n\n\nfunction ToolsModal({ isOpen, onClose, serverName, tools, onUseTool, onToolUpdate, usingToolId }) {\n    const [selectedTool, setSelectedTool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [toolConfigs, setToolConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showConfig, setShowConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 当服务器或工具列表变化时重置选中的工具\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToolsModal.useEffect\": ()=>{\n            setSelectedTool(null);\n            setShowConfig(false);\n        }\n    }[\"ToolsModal.useEffect\"], [\n        serverName,\n        tools\n    ]);\n    // 初始化工具配置\n    const initializeToolConfig = (tool)=>{\n        if (toolConfigs[tool.name]) return;\n        const config = {};\n        if (tool.input_schema) {\n            try {\n                const schema = typeof tool.input_schema === 'string' ? JSON.parse(tool.input_schema) : tool.input_schema;\n                if (schema.properties) {\n                    Object.entries(schema.properties).forEach(([key, prop])=>{\n                        config[key] = prop.default || '';\n                    });\n                }\n            } catch (error) {\n                console.error('解析工具参数模式失败:', error);\n            }\n        }\n        setToolConfigs((prev)=>({\n                ...prev,\n                [tool.name]: config\n            }));\n    };\n    // 更新工具配置\n    const updateToolConfig = (toolName, key, value)=>{\n        setToolConfigs((prev)=>({\n                ...prev,\n                [toolName]: {\n                    ...prev[toolName],\n                    [key]: value\n                }\n            }));\n    };\n    // 使用工具\n    const handleUseTool = (tool)=>{\n        const config = toolConfigs[tool.name] || {};\n        const toolWithConfig = {\n            ...tool,\n            configuredArgs: config\n        };\n        onUseTool(toolWithConfig);\n    };\n    // 保存工具配置到数据库\n    const saveToolConfig = async (tool)=>{\n        try {\n            const config = toolConfigs[tool.name] || {};\n            const response = await fetch('/api/mcp/tool-config', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    toolId: tool.id,\n                    serverName,\n                    toolName: tool.name,\n                    config\n                })\n            });\n            if (response.ok) {\n                alert('工具配置已保存');\n            } else {\n                alert('保存配置失败');\n            }\n        } catch (error) {\n            console.error('保存工具配置失败:', error);\n            alert('保存配置失败');\n        }\n    };\n    // 切换工具启用状态\n    const toggleToolEnabled = async (tool)=>{\n        try {\n            const newEnabled = !tool.enabled;\n            const response = await fetch(`/api/mcp/tools/${tool.id}`, {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    enabled: newEnabled\n                })\n            });\n            if (response.ok) {\n                const result = await response.json();\n                if (result.success && onToolUpdate) {\n                    onToolUpdate(result.tool);\n                }\n            } else {\n                console.error('更新工具状态失败');\n            }\n        } catch (error) {\n            console.error('更新工具状态失败:', error);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                            children: [\n                                                serverName,\n                                                \" - 工具列表\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"共 \",\n                                                tools.length,\n                                                \" 个工具\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-1/2 border-r border-gray-200 dark:border-gray-700 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: !Array.isArray(tools) || tools.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400\",\n                                            children: \"暂无可用工具\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: tools.map((tool, index)=>{\n                                        const toolKey = tool.id ? tool.id.toString() : `${tool.name}-${index}`;\n                                        const isSelected = selectedTool?.name === tool.name;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>{\n                                                setSelectedTool(tool);\n                                                initializeToolConfig(tool);\n                                            },\n                                            className: `border rounded-lg p-3 cursor-pointer transition-all ${isSelected ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"font-medium text-gray-900 dark:text-white text-sm\",\n                                                                children: tool.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-400 mt-1\",\n                                                                children: tool.description || '无描述'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 ml-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                toggleToolEnabled(tool);\n                                                            },\n                                                            title: tool.enabled ? \"禁用工具\" : \"启用工具\",\n                                                            className: `p-1 rounded-md transition-colors ${tool.enabled ? 'text-green-600 hover:text-red-600 hover:bg-red-50' : 'text-gray-400 hover:text-green-600 hover:bg-green-50'}`,\n                                                            children: tool.enabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 47\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 79\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                            lineNumber: 208,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, toolKey, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 23\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-1/2 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: !selectedTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400\",\n                                            children: \"选择工具查看详情\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white mb-2\",\n                                                    children: selectedTool.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                                    children: selectedTool.description || '无描述'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this),\n                                        selectedTool.input_schema && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white mb-3\",\n                                                    children: \"参数配置\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 23\n                                                }, this),\n                                                (()=>{\n                                                    try {\n                                                        const schema = typeof selectedTool.input_schema === 'string' ? JSON.parse(selectedTool.input_schema) : selectedTool.input_schema;\n                                                        if (!schema.properties) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"该工具无需参数\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 31\n                                                            }, this);\n                                                        }\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: Object.entries(schema.properties).map(([key, prop])=>{\n                                                                const currentValue = toolConfigs[selectedTool.name]?.[key] || '';\n                                                                const isRequired = schema.required?.includes(key);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                                            children: [\n                                                                                key,\n                                                                                isRequired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-500 ml-1\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 54\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mb-2\",\n                                                                            children: prop.description || '无描述'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        prop.type === 'boolean' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: currentValue.toString(),\n                                                                            onChange: (e)=>updateToolConfig(selectedTool.name, key, e.target.value === 'true'),\n                                                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"false\",\n                                                                                    children: \"false\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 41\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"true\",\n                                                                                    children: \"true\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                                    lineNumber: 296,\n                                                                                    columnNumber: 41\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 39\n                                                                        }, this) : prop.enum ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            value: currentValue,\n                                                                            onChange: (e)=>updateToolConfig(selectedTool.name, key, e.target.value),\n                                                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"请选择...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 41\n                                                                                }, this),\n                                                                                prop.enum.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: option,\n                                                                                        children: option\n                                                                                    }, option, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                                        lineNumber: 310,\n                                                                                        columnNumber: 43\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 39\n                                                                        }, this) : prop.type === 'number' || prop.type === 'integer' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: currentValue,\n                                                                            onChange: (e)=>updateToolConfig(selectedTool.name, key, prop.type === 'integer' ? parseInt(e.target.value) || 0 : parseFloat(e.target.value) || 0),\n                                                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\",\n                                                                            placeholder: prop.default?.toString() || ''\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                            lineNumber: 316,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: currentValue,\n                                                                            onChange: (e)=>updateToolConfig(selectedTool.name, key, e.target.value),\n                                                                            className: \"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\",\n                                                                            placeholder: prop.default?.toString() || '',\n                                                                            rows: prop.type === 'string' && prop.format === 'textarea' ? 3 : 1\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 39\n                                                                        }, this)\n                                                                    ]\n                                                                }, key, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 35\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 29\n                                                        }, this);\n                                                    } catch (error) {\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-500\",\n                                                            children: \"参数模式解析失败\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 29\n                                                        }, this);\n                                                    }\n                                                })()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleUseTool(selectedTool),\n                                                disabled: usingToolId === selectedTool.name,\n                                                title: usingToolId === selectedTool.name ? \"工具执行中...\" : \"使用工具\",\n                                                className: `p-3 rounded-full transition-colors ${usingToolId === selectedTool.name ? 'bg-blue-100 text-blue-600 cursor-not-allowed' : 'bg-blue-600 text-white hover:bg-blue-700'}`,\n                                                children: usingToolId === selectedTool.name ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Play_Power_PowerOff_Settings_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n            lineNumber: 147,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\components\\\\ToolsModal.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mcp-config/components/ToolsModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/mcp-config/hooks/useMcpConfig.ts":
/*!**************************************************!*\
  !*** ./src/app/mcp-config/hooks/useMcpConfig.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMcpConfig: () => (/* binding */ useMcpConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useMcpConfig() {\n    const [servers, setServers] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [tools, setTools] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [toolsLoading, setToolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [showToolsModal, setShowToolsModal] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('all');\n    const [selectedServer, setSelectedServer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [executionResult, setExecutionResult] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [usingToolId, setUsingToolId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [newServer, setNewServer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        name: '',\n        display_name: '',\n        type: 'stdio',\n        description: '',\n        enabled: true,\n        command: '',\n        args: '[]',\n        working_directory: '',\n        url: '',\n        base_url: '',\n        port: undefined,\n        path: '/',\n        protocol: 'http'\n    });\n    // 加载服务器列表\n    const loadServers = async (forceRefresh = false)=>{\n        try {\n            setLoading(true);\n            const url = forceRefresh ? '/api/mcp/server-list?refresh=true' : '/api/mcp/server-list';\n            const response = await fetch(url);\n            const data = await response.json();\n            if (data.success) {\n                setServers(data.servers || []);\n            } else {\n                console.error('加载服务器列表失败:', data.error);\n                setServers([]);\n            }\n        } catch (error) {\n            console.error('加载服务器列表失败:', error);\n            setServers([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载工具列表\n    const loadTools = async (serverName, forceRefresh = false)=>{\n        if (!serverName) {\n            setTools([]);\n            return;\n        }\n        try {\n            setToolsLoading(true);\n            const params = new URLSearchParams({\n                server: serverName,\n                includeDisabled: 'true' // 配置页面需要显示所有工具（包括禁用的）\n            });\n            if (forceRefresh) {\n                params.append('refresh', 'true');\n            }\n            const response = await fetch(`/api/mcp/tools?${params}`);\n            const data = await response.json();\n            if (data.success && Array.isArray(data.tools)) {\n                setTools(data.tools);\n            } else {\n                console.error('加载工具列表失败:', data.error);\n                setTools([]);\n            }\n        } catch (error) {\n            console.error('加载工具列表失败:', error);\n            setTools([]);\n        } finally{\n            setToolsLoading(false);\n        }\n    };\n    // 切换标签页\n    const handleTabChange = async (tab)=>{\n        setSelectedTab(tab);\n        setSelectedServer(null);\n        // 切换标签页时清空工具列表\n        setTools([]);\n    };\n    // 选择服务器 - 改为弹窗显示工具列表\n    const handleServerSelect = async (serverName)=>{\n        setSelectedServer(serverName);\n        await loadTools(serverName);\n        setShowToolsModal(true);\n    };\n    // 检查服务器连接状态\n    const checkServerStatus = async (serverName)=>{\n        try {\n            const response = await fetch('/api/mcp/server-status', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    serverName\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                // 更新服务器状态\n                setServers((prev)=>prev.map((server)=>server.name === serverName ? {\n                            ...server,\n                            status: data.status,\n                            toolCount: data.toolCount,\n                            errorMessage: data.errorMessage\n                        } : server));\n                // 如果连接成功，重新加载服务器列表以获取最新的工具数量\n                if (data.status === 'connected') {\n                    setTimeout(()=>{\n                        loadServers(true); // 强制刷新服务器列表\n                    }, 1000); // 延迟1秒确保数据库更新完成\n                }\n                return data;\n            }\n        } catch (error) {\n            console.error('检查服务器状态失败:', error);\n        }\n        return null;\n    };\n    // 刷新工具列表\n    const refreshTools = async ()=>{\n        if (selectedServer) {\n            await loadTools(selectedServer, true);\n        }\n    };\n    // 删除工具（MCP工具通常不支持删除，这里保留接口但可能不会实际使用）\n    const handleDeleteTool = async (toolId)=>{\n        // MCP工具通常不支持删除，这里可以添加提示\n        console.log('MCP工具不支持删除操作');\n    };\n    // 使用工具\n    const handleUseTool = async (tool)=>{\n        try {\n            setUsingToolId(tool.name); // 设置loading状态\n            // 使用已配置的参数，如果没有配置则为空对象\n            const params = tool.configuredArgs || {};\n            // 调用工具\n            const response = await fetch('/api/mcp/call-tool', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: tool.name,\n                    serverName: selectedServer || 'local',\n                    arguments: params\n                })\n            });\n            if (response.ok) {\n                const result = await response.json();\n                console.log('工具执行成功:', result);\n                // 可以在这里添加更好的结果显示方式，比如在页面上显示结果\n                setExecutionResult({\n                    success: true,\n                    data: result,\n                    toolName: tool.name\n                });\n            } else {\n                const error = await response.json();\n                console.error('工具执行失败:', error);\n                setExecutionResult({\n                    success: false,\n                    error: error.error || '未知错误',\n                    toolName: tool.name\n                });\n            }\n        } catch (error) {\n            console.error('使用工具失败:', error);\n            alert('工具执行失败，请检查控制台获取详细信息');\n        } finally{\n            setUsingToolId(null); // 清除loading状态\n        }\n    };\n    // 检查服务器是否已存在\n    const checkServerExists = (serverName)=>{\n        return servers.some((server)=>server.name === serverName);\n    };\n    // 删除服务器\n    const handleDeleteServer = async (serverName)=>{\n        // 本地服务器不允许删除\n        if (serverName === 'local') {\n            alert('本地服务器不支持删除操作');\n            return;\n        }\n        if (!confirm(`确定要删除服务器 \"${serverName}\" 吗？此操作不可撤销。`)) {\n            return;\n        }\n        try {\n            // 先找到要删除的服务器，获取其ID\n            const targetServer = servers.find((server)=>server.name === serverName);\n            if (!targetServer || !targetServer.id) {\n                alert('找不到要删除的服务器');\n                return;\n            }\n            // 先删除数据库记录\n            const response = await fetch(`/api/mcp/servers/${targetServer.id}`, {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // 然后删除配置文件中的记录\n                const configResponse = await fetch('/api/mcp/config', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        action: 'delete',\n                        serverName: serverName\n                    })\n                });\n                if (configResponse.ok) {\n                    // 如果删除的是当前选中的服务器，清空选择\n                    if (selectedServer === serverName) {\n                        setSelectedServer(null);\n                        setTools([]);\n                    }\n                    await loadServers();\n                } else {\n                    const configErrorData = await configResponse.json();\n                    console.error('删除配置文件失败:', configErrorData.error || '未知错误');\n                }\n            } else {\n                const errorData = await response.json();\n                console.error('删除服务器失败:', errorData.error || '未知错误');\n            }\n        } catch (error) {\n            console.error('删除服务器失败:', error);\n        }\n    };\n    // 添加新服务器（保留原有功能，增加重复检查）\n    const handleAddServer = async (serverData)=>{\n        try {\n            // 使用传入的serverData或当前的newServer状态\n            const serverToAdd = serverData || newServer;\n            // 检查服务器名称是否已存在\n            if (checkServerExists(serverToAdd.name)) {\n                alert(`服务器 \"${serverToAdd.name}\" 已存在，请使用不同的名称`);\n                return;\n            }\n            console.log('准备发送的服务器数据:', JSON.stringify(serverToAdd, null, 2));\n            const response = await fetch('/api/mcp/servers', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(serverToAdd)\n            });\n            if (response.ok) {\n                setShowAddModal(false);\n                setNewServer({\n                    name: '',\n                    display_name: '',\n                    type: 'stdio',\n                    description: '',\n                    enabled: true,\n                    command: '',\n                    args: '[]',\n                    working_directory: '',\n                    url: '',\n                    base_url: '',\n                    port: undefined,\n                    path: '/',\n                    protocol: 'http'\n                });\n                await loadServers();\n                // 如果当前选中的服务器就是新添加的服务器，刷新工具列表\n                if (selectedServer === newServer.name) {\n                    await loadTools(selectedServer, true);\n                }\n            // 移除成功弹窗，静默完成操作\n            } else {\n                const errorData = await response.json();\n                alert(`添加服务器失败: ${errorData.error || '未知错误'}`);\n            }\n        } catch (error) {\n            console.error('添加服务器失败:', error);\n            alert('添加服务器失败，请检查网络连接');\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMcpConfig.useEffect\": ()=>{\n            loadServers();\n        }\n    }[\"useMcpConfig.useEffect\"], []);\n    return {\n        servers,\n        tools,\n        loading,\n        toolsLoading,\n        showAddModal,\n        showToolsModal,\n        selectedTab,\n        selectedServer,\n        newServer,\n        setShowAddModal,\n        setShowToolsModal,\n        setNewServer,\n        setTools,\n        loadServers,\n        loadTools,\n        handleTabChange,\n        handleServerSelect,\n        checkServerStatus,\n        refreshTools,\n        handleDeleteTool,\n        handleUseTool,\n        handleAddServer,\n        handleDeleteServer,\n        executionResult,\n        setExecutionResult,\n        usingToolId\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mcp-config/hooks/useMcpConfig.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/mcp-config/page.tsx":
/*!*************************************!*\
  !*** ./src/app/mcp-config/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ McpConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hooks/useMcpConfig */ \"(ssr)/./src/app/mcp-config/hooks/useMcpConfig.ts\");\n/* harmony import */ var _components_PageHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/PageHeader */ \"(ssr)/./src/app/mcp-config/components/PageHeader.tsx\");\n/* harmony import */ var _components_AddServerModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/AddServerModal */ \"(ssr)/./src/app/mcp-config/components/AddServerModal.tsx\");\n/* harmony import */ var _components_ToolsModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ToolsModal */ \"(ssr)/./src/app/mcp-config/components/ToolsModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction McpConfigPage() {\n    const { servers, tools, loading, toolsLoading, showAddModal, showToolsModal, selectedTab, selectedServer, newServer, setShowAddModal, setShowToolsModal, setNewServer, setTools, loadServers, loadTools, handleTabChange, handleServerSelect, checkServerStatus, refreshTools, handleDeleteTool, handleUseTool, handleAddServer, handleDeleteServer, executionResult, setExecutionResult, usingToolId } = (0,_hooks_useMcpConfig__WEBPACK_IMPORTED_MODULE_1__.useMcpConfig)();\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-theme-background-secondary flex items-center justify-center transition-colors duration-300\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-theme-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-theme-background-secondary transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PageHeader__WEBPACK_IMPORTED_MODULE_2__.PageHeader, {\n                onAddServer: ()=>setShowAddModal(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 sm:px-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-theme-border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"-mb-px flex space-x-8\",\n                                    children: [\n                                        {\n                                            key: 'all',\n                                            label: '全部',\n                                            count: servers.length\n                                        },\n                                        {\n                                            key: 'local',\n                                            label: '本地服务器',\n                                            count: servers.filter((s)=>s.type === 'stdio').length\n                                        },\n                                        {\n                                            key: 'external',\n                                            label: '外部服务器',\n                                            count: servers.filter((s)=>s.type !== 'stdio').length\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleTabChange(tab.key),\n                                            className: `${selectedTab === tab.key ? 'border-theme-primary text-theme-primary' : 'border-transparent text-theme-foreground-muted hover:text-theme-foreground hover:border-theme-border-secondary'} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: tab.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-theme-background-tertiary text-theme-foreground py-0.5 px-2.5 rounded-full text-xs font-medium\",\n                                                    children: tab.count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, tab.key, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-theme-foreground\",\n                                        children: \"服务器列表\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: servers.filter((server)=>{\n                                        if (selectedTab === 'all') return true;\n                                        if (selectedTab === 'local') return server.type === 'stdio';\n                                        if (selectedTab === 'external') return server.type !== 'stdio';\n                                        return true;\n                                    }).map((server)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `${selectedServer === server.name ? 'ring-2 ring-blue-500 border-blue-500' : 'border-gray-200 hover:border-gray-300'} bg-white border rounded-lg p-4 transition-all duration-200 hover:shadow-md`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: server.displayName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                server.type !== 'stdio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        checkServerStatus(server.name);\n                                                                    },\n                                                                    className: \"text-gray-400 hover:text-gray-600 p-1\",\n                                                                    title: \"检查连接状态\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-3 h-3\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 117,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                        lineNumber: 116,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 108,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                server.name !== 'local' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleDeleteServer(server.name);\n                                                                    },\n                                                                    className: \"text-red-400 hover:text-red-600 p-1\",\n                                                                    title: \"删除服务器\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-3 h-3\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                            lineNumber: 131,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                        lineNumber: 130,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `w-2 h-2 rounded-full ${server.status === 'connected' ? 'bg-green-400' : server.status === 'error' ? 'bg-red-400' : 'bg-gray-400'}`,\n                                                                    title: `状态: ${server.status}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mb-3\",\n                                                    children: server.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this),\n                                                server.errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-red-500 mb-2\",\n                                                    title: server.errorMessage,\n                                                    children: [\n                                                        \"错误: \",\n                                                        server.errorMessage.length > 50 ? server.errorMessage.substring(0, 50) + '...' : server.errorMessage\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: server.type\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        server.toolCount,\n                                                                        \" 工具\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 150,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        handleServerSelect(server.name);\n                                                                    },\n                                                                    className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs\",\n                                                                    children: \"查看工具\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, server.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddServerModal__WEBPACK_IMPORTED_MODULE_3__.AddServerModal, {\n                isOpen: showAddModal,\n                onClose: ()=>setShowAddModal(false),\n                newServer: newServer,\n                onServerChange: setNewServer,\n                onSubmit: handleAddServer\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ToolsModal__WEBPACK_IMPORTED_MODULE_4__.ToolsModal, {\n                isOpen: showToolsModal,\n                onClose: ()=>setShowToolsModal(false),\n                serverName: selectedServer || '',\n                tools: tools,\n                onUseTool: handleUseTool,\n                usingToolId: usingToolId,\n                onToolUpdate: (updatedTool)=>{\n                    // 更新工具列表中的对应工具\n                    setTools((prevTools)=>prevTools.map((tool)=>tool.id === updatedTool.id ? updatedTool : tool));\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            executionResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                    children: [\n                                        \"工具执行结果 - \",\n                                        executionResult.toolName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setExecutionResult(null),\n                                    className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this),\n                        executionResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-green-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"执行成功\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 dark:bg-gray-700 rounded-md p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap\",\n                                        children: JSON.stringify(executionResult.data, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"执行失败\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 dark:bg-red-900/20 rounded-md p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-800 dark:text-red-200\",\n                                        children: executionResult.error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\mcp-config\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/mcp-config/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useThemeToggle: () => (/* binding */ useThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(ssr)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,useThemeToggle auto */ \n\n\n// 创建主题上下文\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// 主题提供者组件\nfunction ThemeProvider({ children, defaultTheme = 'light' }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 设置主题的函数\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.saveTheme)(newTheme);\n    };\n    // 切换主题的函数\n    const toggleTheme = ()=>{\n        const newTheme = theme === 'light' ? 'dark' : 'light';\n        setTheme(newTheme);\n    };\n    // 初始化主题\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const initialTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.initializeTheme)();\n            setThemeState(initialTheme);\n            setIsInitialized(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // 监听系统主题变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!isInitialized) return;\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": (e)=>{\n                    // 只有在没有手动设置主题时才跟随系统主题\n                    const savedTheme = localStorage.getItem('theme');\n                    if (!savedTheme) {\n                        const systemTheme = e.matches ? 'dark' : 'light';\n                        setTheme(systemTheme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        isInitialized\n    ]);\n    // 提供主题配置\n    const themeConfig = {\n        theme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: themeConfig,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n// 使用主题的Hook\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n// 主题切换Hook（简化版本）\nfunction useThemeToggle() {\n    const { theme, toggleTheme } = useTheme();\n    return {\n        theme,\n        toggleTheme,\n        isDark: theme === 'dark',\n        isLight: theme === 'light'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/theme.ts":
/*!**************************!*\
  !*** ./src/lib/theme.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyTheme: () => (/* binding */ applyTheme),\n/* harmony export */   getSavedTheme: () => (/* binding */ getSavedTheme),\n/* harmony export */   getSystemTheme: () => (/* binding */ getSystemTheme),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   saveTheme: () => (/* binding */ saveTheme),\n/* harmony export */   themeColors: () => (/* binding */ themeColors)\n/* harmony export */ });\n// 主题类型定义\n// 主题颜色方案\nconst themeColors = {\n    light: {\n        // 背景色\n        background: '#ffffff',\n        backgroundSecondary: '#f8fafc',\n        backgroundTertiary: '#f1f5f9',\n        // 前景色\n        foreground: '#0f172a',\n        foregroundSecondary: '#334155',\n        foregroundMuted: '#64748b',\n        // 边框色\n        border: '#e2e8f0',\n        borderSecondary: '#cbd5e1',\n        // 卡片背景\n        card: '#ffffff',\n        cardHover: '#f8fafc',\n        // 输入框\n        input: '#ffffff',\n        inputBorder: '#d1d5db',\n        inputFocus: '#3b82f6',\n        // 按钮\n        primary: '#3b82f6',\n        primaryHover: '#2563eb',\n        secondary: '#6b7280',\n        secondaryHover: '#4b5563',\n        // 状态色\n        success: '#10b981',\n        warning: '#f59e0b',\n        error: '#ef4444',\n        info: '#3b82f6',\n        // 特殊色\n        accent: '#8b5cf6',\n        accentHover: '#7c3aed'\n    },\n    dark: {\n        // 背景色\n        background: '#0f172a',\n        backgroundSecondary: '#1e293b',\n        backgroundTertiary: '#334155',\n        // 前景色\n        foreground: '#f8fafc',\n        foregroundSecondary: '#e2e8f0',\n        foregroundMuted: '#94a3b8',\n        // 边框色\n        border: '#334155',\n        borderSecondary: '#475569',\n        // 卡片背景\n        card: '#1e293b',\n        cardHover: '#334155',\n        // 输入框\n        input: '#1e293b',\n        inputBorder: '#475569',\n        inputFocus: '#3b82f6',\n        // 按钮\n        primary: '#3b82f6',\n        primaryHover: '#2563eb',\n        secondary: '#6b7280',\n        secondaryHover: '#9ca3af',\n        // 状态色\n        success: '#10b981',\n        warning: '#f59e0b',\n        error: '#ef4444',\n        info: '#3b82f6',\n        // 特殊色\n        accent: '#8b5cf6',\n        accentHover: '#7c3aed'\n    }\n};\n// 获取系统主题偏好\nconst getSystemTheme = ()=>{\n    if (false) {}\n    return 'light';\n};\n// 从localStorage获取保存的主题\nconst getSavedTheme = ()=>{\n    if (false) {}\n    return null;\n};\n// 保存主题到localStorage\nconst saveTheme = (theme)=>{\n    if (false) {}\n};\n// 应用主题到DOM（带过渡效果）\nconst applyTheme = (theme)=>{\n    if (false) {}\n};\n// 初始化主题\nconst initializeTheme = ()=>{\n    const savedTheme = getSavedTheme();\n    const theme = savedTheme || getSystemTheme();\n    applyTheme(theme);\n    return theme;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/theme.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmcp-config%2Fpage&page=%2Fmcp-config%2Fpage&appPaths=%2Fmcp-config%2Fpage&pagePath=private-next-app-dir%2Fmcp-config%2Fpage.tsx&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();