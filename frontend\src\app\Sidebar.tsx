'use client';

import React from 'react';
import { Plus, Trash2, MessageSquare, <PERSON>tings, Server } from 'lucide-react';
import { Conversation } from '@/lib/database';
import { OllamaModel } from './chat/types';
import { ThemeToggle } from '@/components/ThemeToggle';
import Link from 'next/link';

interface SidebarProps {
  models: OllamaModel[];
  conversations: Conversation[];
  currentConversation: Conversation | null;
  selectedModel: string;
  showModelSelector: boolean;
  onModelChange: (model: string) => void;
  onShowModelSelector: (show: boolean) => void;
  onCreateConversation: () => void;
  onLoadConversation: (conversationId: number) => void;
  onDeleteConversation: (conversationId: number) => void;
}

export function Sidebar({
  models,
  conversations,
  currentConversation,
  selectedModel,
  showModelSelector,
  onModelChange,
  onShowModelSelector,
  onCreateConversation,
  onLoadConversation,
  onDeleteConversation,
}: SidebarProps) {
  return (
    <div className="w-80 bg-theme-card border-r border-theme-border flex flex-col transition-colors duration-300">
      {/* 头部 */}
      <div className="p-4 border-b border-theme-border">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-bold text-theme-foreground">
            Kun Agent
          </h1>
          <div className="flex items-center gap-2">
            <ThemeToggle variant="icon" size="sm" />
            <button
              onClick={() => onShowModelSelector(!showModelSelector)}
              className="p-2 text-theme-foreground-muted hover:text-theme-foreground transition-colors duration-200"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        {/* 模型选择器 */}
        {showModelSelector && (
          <div className="mb-4 p-3 bg-theme-background-tertiary rounded-lg transition-colors duration-300">
            <label className="block text-sm font-medium text-theme-foreground-secondary mb-2">
              选择模型
            </label>
            <select
              value={selectedModel}
              onChange={(e) => onModelChange(e.target.value)}
              className="w-full p-2 border border-theme-input-border rounded-md bg-theme-input text-theme-foreground focus:border-theme-input-focus focus:ring-1 focus:ring-theme-input-focus transition-colors duration-200"
            >
              {models.map((model) => (
                <option key={model.name} value={model.name}>
                  {model.name} ({(model.size / 1024 / 1024 / 1024).toFixed(1)}GB)
                </option>
              ))}
            </select>
          </div>
        )}
        
        <button
          onClick={onCreateConversation}
          disabled={!selectedModel}
          className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed mb-2 transition-colors duration-200"
        >
          <Plus className="w-4 h-4" />
          新建对话
        </button>

        <Link href="/mcp-config">
          <button className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-theme-success text-white rounded-lg hover:bg-green-700 transition-colors duration-200">
            <Server className="w-4 h-4" />
            MCP配置
          </button>
        </Link>
      </div>
      
      {/* 对话列表 */}
      <div className="flex-1 overflow-y-auto">
        {conversations.map((conversation) => (
          <div
            key={conversation.id}
            className={`p-3 border-b border-theme-border cursor-pointer hover:bg-theme-card-hover group transition-colors duration-200 ${
              currentConversation?.id === conversation.id
                ? 'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-theme-primary'
                : ''
            }`}
            onClick={() => onLoadConversation(conversation.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-4 h-4 text-theme-foreground-muted flex-shrink-0" />
                  <span className="text-sm font-medium text-theme-foreground truncate">
                    {conversation.title}
                  </span>
                </div>
                <p className="text-xs text-theme-foreground-muted mt-1">
                  {conversation.model} • {new Date(conversation.updated_at).toLocaleDateString()}
                </p>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteConversation(conversation.id);
                }}
                className="p-1 text-theme-foreground-muted hover:text-theme-error opacity-0 group-hover:opacity-100 transition-all duration-200"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}