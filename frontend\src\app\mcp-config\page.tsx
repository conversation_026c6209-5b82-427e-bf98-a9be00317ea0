'use client';

import { useMcpConfig } from './hooks/useMcpConfig';
import { PageHeader } from './components/PageHeader';
import { ToolsList } from './components/ToolsList';
import { AddServerModal } from './components/AddServerModal';
import { ToolsModal } from './components/ToolsModal';

export default function McpConfigPage() {
  const {
    servers,
    tools,
    loading,
    toolsLoading,
    showAddModal,
    showToolsModal,
    selectedTab,
    selectedServer,
    newServer,
    setShowAddModal,
    setShowToolsModal,
    setNewServer,
    setTools,
    loadServers,
    loadTools,
    handleTabChange,
    handleServerSelect,
    checkServerStatus,
    refreshTools,
    handleDeleteTool,
    handleUseTool,
    handleAddServer,
    handleDeleteServer,
    executionResult,
    setExecutionResult,
    usingToolId
  } = useMcpConfig();

  if (loading) {
    return (
      <div className="min-h-screen bg-theme-background-secondary flex items-center justify-center transition-colors duration-300">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-theme-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-theme-background-secondary transition-colors duration-300">
      <PageHeader onAddServer={() => setShowAddModal(true)} />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 标签页 */}
          <div className="mb-6">
            <div className="border-b border-theme-border">
              <nav className="-mb-px flex space-x-8">
                {[
                  { key: 'all', label: '全部', count: servers.length },
                  { key: 'local', label: '本地服务器', count: servers.filter(s => s.type === 'stdio').length },
                  { key: 'external', label: '外部服务器', count: servers.filter(s => s.type !== 'stdio').length }
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => handleTabChange(tab.key as any)}
                    className={`${
                      selectedTab === tab.key
                        ? 'border-theme-primary text-theme-primary'
                        : 'border-transparent text-theme-foreground-muted hover:text-theme-foreground hover:border-theme-border-secondary'
                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors duration-200`}
                  >
                    <span>{tab.label}</span>
                    <span className="bg-theme-background-tertiary text-theme-foreground py-0.5 px-2.5 rounded-full text-xs font-medium">
                      {tab.count}
                    </span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* 服务器卡片 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-theme-foreground">服务器列表</h3>

            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {servers
                .filter(server => {
                  if (selectedTab === 'all') return true;
                  if (selectedTab === 'local') return server.type === 'stdio';
                  if (selectedTab === 'external') return server.type !== 'stdio';
                  return true;
                })
                .map((server) => (
                  <div
                    key={server.name}
                    className={`${
                      selectedServer === server.name
                        ? 'ring-2 ring-blue-500 border-blue-500'
                        : 'border-gray-200 hover:border-gray-300'
                    } bg-white border rounded-lg p-4 transition-all duration-200 hover:shadow-md`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900">{server.displayName}</h4>
                      <div className="flex items-center space-x-2">
                        {server.type !== 'stdio' && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              checkServerStatus(server.name);
                            }}
                            className="text-gray-400 hover:text-gray-600 p-1"
                            title="检查连接状态"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                          </button>
                        )}
                        {server.name !== 'local' && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteServer(server.name);
                            }}
                            className="text-red-400 hover:text-red-600 p-1"
                            title="删除服务器"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        )}
                        <div className={`w-2 h-2 rounded-full ${
                          server.status === 'connected' ? 'bg-green-400' :
                          server.status === 'error' ? 'bg-red-400' : 'bg-gray-400'
                        }`} title={`状态: ${server.status}`}></div>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mb-3">{server.description}</p>
                    {server.errorMessage && (
                      <p className="text-xs text-red-500 mb-2" title={server.errorMessage}>
                        错误: {server.errorMessage.length > 50 ? server.errorMessage.substring(0, 50) + '...' : server.errorMessage}
                      </p>
                    )}
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">{server.type}</span>
                      <div className="flex items-center space-x-2">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          {server.toolCount} 工具
                        </span>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleServerSelect(server.name);
                          }}
                          className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs"
                        >
                          查看工具
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              }
            </div>
          </div>


        </div>
      </main>

      <AddServerModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        newServer={newServer}
        onServerChange={setNewServer}
        onSubmit={handleAddServer}
      />
      
      <ToolsModal
        isOpen={showToolsModal}
        onClose={() => setShowToolsModal(false)}
        serverName={selectedServer || ''}
        tools={tools}
        onUseTool={handleUseTool}
        usingToolId={usingToolId}
        onToolUpdate={(updatedTool) => {
          // 更新工具列表中的对应工具
          setTools(prevTools => 
            prevTools.map(tool => 
              tool.id === updatedTool.id ? updatedTool : tool
            )
          );
        }}
      />
      
      {/* 工具执行结果显示 */}
      {executionResult && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                工具执行结果 - {executionResult.toolName}
              </h3>
              <button
                onClick={() => setExecutionResult(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                ✕
              </button>
            </div>
            
            {executionResult.success ? (
              <div className="space-y-3">
                <div className="flex items-center text-green-600">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  执行成功
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-4">
                  <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                    {JSON.stringify(executionResult.data, null, 2)}
                  </pre>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center text-red-600">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  执行失败
                </div>
                <div className="bg-red-50 dark:bg-red-900/20 rounded-md p-4">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {executionResult.error}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}