'use client';

import React, { useState, useEffect } from 'react';
import { Brain, ChevronDown, ChevronRight, Loader2, Check } from 'lucide-react';

interface ThinkingModeProps {
  content: string;
  isExpanded: boolean;
  onToggleExpand: () => void;
  defaultHidden?: boolean;
}

export function ThinkingMode({
  content,
  isExpanded,
  onToggleExpand,
  defaultHidden = false
}: ThinkingModeProps) {
  const [initiallyHidden, setInitiallyHidden] = useState(defaultHidden);
  const [thinkingContent, setThinkingContent] = useState('');

  // 提取<think></think>标签内的内容，支持流式渲染
  const extractThinkingContent = (text: string): string => {
    const thinkRegex = /<think>([\s\S]*?)(?:<\/think>|$)/g;
    const matches = text.match(thinkRegex);
    if (!matches) return '';

    return matches
      .map(match => {
        return match.replace(/<\/?think>/g, '');
      })
      .join('\n\n');
  };



  // 检测思考状态变化
  useEffect(() => {
    const newExtracted = extractThinkingContent(content);
    setThinkingContent(newExtracted);

    const hasThinkStart = /<think>/.test(content);

    // 如果有思考内容或正在思考，确保组件可见
    if (newExtracted || hasThinkStart) {
      if (initiallyHidden) {
        setInitiallyHidden(false);
        console.log('🔍 检测到思考内容，显示思考面板');
      }
    }
  }, [content, initiallyHidden]);



  // 如果初始隐藏，且没有思考内容，则不渲染
  if (initiallyHidden && !thinkingContent) {
    return null;
  }

  // 检测是否正在思考中（有开始标签但没有结束标签）
  const isCurrentlyThinking = /<think>/.test(content) && !/<\/think>/.test(content);

  return (
    <div className="border border-purple-200 dark:border-purple-700 rounded-lg bg-purple-50 dark:bg-purple-900/10">
      {/* 标题栏 */}
      <div
        onClick={onToggleExpand}
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-800/20 transition-colors"
      >
        <div className="flex items-center gap-2">
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          ) : (
            <ChevronRight className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          )}
          
          <Brain className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
            思考模式
          </span>
          

        </div>
        
        {/* 状态图标显示 */}
        <div className="flex items-center">
          {isCurrentlyThinking ? (
            <Loader2 className="w-4 h-4 text-purple-600 dark:text-purple-400 animate-spin" />
          ) : (
            <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
          )}
        </div>
      </div>

      {/* 思考内容 */}
      {isExpanded && (
        <div className="border-t border-purple-200 dark:border-purple-700 p-3">
          {/* 思考中的动画 */}
          {isCurrentlyThinking && !thinkingContent && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="w-6 h-6 text-purple-600 dark:text-purple-400 animate-spin" />
            </div>
          )}

          {/* 思考内容 */}
          {thinkingContent && (
            <div className="text-sm text-purple-700 dark:text-purple-300 whitespace-pre-wrap bg-white dark:bg-gray-800 dark:border-purple-600">
              {thinkingContent}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// 导出辅助函数供其他组件使用
export const hasThinkingContent = (content: string): boolean => {
  return /<think>/.test(content);
};

export const removeThinkingContent = (content: string): string => {
  return content.replace(/<think>[\s\S]*?(?:<\/think>|$)/g, '').trim();
};