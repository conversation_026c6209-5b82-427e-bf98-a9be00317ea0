'use client';

import React from 'react';
import { <PERSON><PERSON>, User, Wrench, Info } from 'lucide-react';
import { Message } from '@/lib/database';
import { AIState } from '../types';
import { AIStatusIndicator } from './AIStatusIndicator';
import { ThinkingMode, hasThinkingContent, removeThinkingContent } from './ThinkingMode';


interface MessageItemProps {
  message: Message;
  messageIndex?: number;
  showAIStatus?: boolean;
  aiState?: AIState;
  activeToolCalls?: Map<string, any>;
  thinkingStartTime?: number;
}

export function MessageItem({ message, messageIndex, showAIStatus, aiState, activeToolCalls, thinkingStartTime }: MessageItemProps) {
  const [isThinkingExpanded, setIsThinkingExpanded] = React.useState(false);
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';
  const isTool = message.role === 'tool';
  const isToolCall = message.role === 'tool_call';
  
  // tool_call类型的消息现在由独立的ToolCallMessage组件处理，这里不再渲染
  if (isToolCall) {
    return null;
  }

  // 检测并渲染图片URL
  const renderImageIfUrl = (text: string) => {
    const imageUrlRegex = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp|svg))/gi;
    const matches = text.match(imageUrlRegex);
    
    if (matches) {
      const parts = text.split(imageUrlRegex);
      return (
        <div className="space-y-2">
          {parts.map((part, index) => {
            if (imageUrlRegex.test(part)) {
              return (
                <div key={index} className="my-2">
                  <img 
                    src={part} 
                    alt="工具返回的图片" 
                    className="max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className="hidden text-sm text-gray-500 italic">
                    图片加载失败: {part}
                  </div>
                </div>
              );
            }
            return part ? <span key={index}>{part}</span> : null;
          })}
        </div>
      );
    }
    
    return <span>{text}</span>;
  };

  // 渲染工具调用消息
  const renderToolCallContent = () => {
    if (!isToolCall) return null;

    // 解析工具相关数据
    const toolName = (message as any).tool_name || '未知工具';
    const toolArgs = (message as any).tool_args ? JSON.parse((message as any).tool_args) : {};
    const toolResult = (message as any).tool_result ? JSON.parse((message as any).tool_result) : null;
    const toolStatus = (message as any).tool_status || 'executing';
    const toolError = (message as any).tool_error;
    const executionTime = (message as any).tool_execution_time;

    const getStatusColor = () => {
      switch (toolStatus) {
        case 'executing':
          return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200';
        case 'completed':
          return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200';
        case 'error':
          return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200';
        default:
          return 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200';
      }
    };

    return (
      <div className="space-y-3">
        <div>
          <div className="text-sm font-medium mb-1">🔧 {toolName}</div>
          <div className="text-xs opacity-75">
            <strong>参数：</strong>
            <pre className="mt-1 text-xs bg-black/5 dark:bg-white/5 p-2 rounded overflow-x-auto">
              {JSON.stringify(toolArgs, null, 2)}
            </pre>
          </div>
        </div>

        {toolStatus === 'executing' && (
          <div className="text-sm text-blue-600 dark:text-blue-400">
            ⏳ 正在执行...
          </div>
        )}

        {toolStatus === 'error' && toolError && (
          <div className="p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded">
            <div className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">错误信息：</div>
            <div className="text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap">
              {toolError}
            </div>
          </div>
        )}

        {toolStatus === 'completed' && toolResult && (
          <div className="p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded">
            <div className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">执行结果：</div>
            <div className="text-sm text-gray-700 dark:text-gray-300 max-h-96 overflow-y-auto">
              <div className="whitespace-pre-wrap">
                {renderImageIfUrl(typeof toolResult === 'string' ? toolResult : JSON.stringify(toolResult, null, 2))}
              </div>
            </div>
          </div>
        )}

        {executionTime && (
          <div className="text-xs text-gray-500">
            执行时间: {executionTime}ms
          </div>
        )}
      </div>
    );
  };

  // 工具调用结果现在由ToolCallMessage组件处理，这里不再显示
  const renderToolContent = (_content: string) => {
    // 工具消息现在由独立的ToolCallMessage组件处理，这里返回空内容
    return <div className="text-gray-500 italic text-sm">工具调用结果由独立组件显示</div>;
  };

  // 处理助手消息内容（智能清理工具调用相关逻辑）
  const renderAssistantContent = (content: string) => {
    // 智能清理工具调用相关的内容，只清理type为'function'的工具调用
    let cleanContent = content;
    
    // 检测并清理各种工具调用格式
    const functionToolCallRegex = /```json\n{[\s\S]*?"type":\s*["']function["'][\s\S]*?}\n```/g;
    const mcpToolCallRegex = /调用工具:\s*.*?\n参数:\s*[\s\S]*?\n\n(?:结果|错误|状态):\s*[\s\S]*?(?=\n\n|$)/g;
    
    // 清理function类型的工具调用JSON格式
    cleanContent = cleanContent.replace(functionToolCallRegex, '');
    
    // 清理MCP工具调用格式
    cleanContent = cleanContent.replace(mcpToolCallRegex, '');
    
    // 清理其他工具调用相关的状态信息
    cleanContent = cleanContent
      .replace(/工具调用完成[\s\S]*?(?=\n\n|$)/g, '')
      .replace(/工具执行结果[\s\S]*?(?=\n\n|$)/g, '')
      .replace(/🔧\s*.*?\n[\s\S]*?(?=\n\n|$)/g, '') // 清理工具图标开头的内容
      .replace(/执行工具[\s\S]*?(?=\n\n|$)/g, '')
      .replace(/Tool\s+call[\s\S]*?(?=\n\n|$)/gi, '')
      .replace(/\n{3,}/g, '\n\n') // 清理多余的换行
      .trim();
    
    // 检查是否包含思考内容或正在思考中
    const hasThinking = hasThinkingContent(cleanContent);
    const isCurrentlyThinking = showAIStatus && aiState?.status === 'thinking';
    const isGeneratingWithThinking = showAIStatus && aiState?.status === 'generating' && hasThinking;

    // 检测思考状态：不仅依赖aiState，也要检查内容中的思考标签
    const hasThinkingInProgress = /<think>/.test(cleanContent) && !/<\/think>/.test(cleanContent);
    const isThinkingAnyway = hasThinkingInProgress || isCurrentlyThinking || isGeneratingWithThinking;
    
    // 移除思考标签，获取清理后的内容用于显示
    const contentWithoutThinking = removeThinkingContent(cleanContent);
    
    // 如果正在思考但还没有内容，仍然要显示思考面板
    const shouldShow = contentWithoutThinking || isThinkingAnyway || hasThinking;

    if (!shouldShow) {
      return null;
    }

    return (
      <div className="space-y-2">
        {/* 思考模式组件 - 优化显示时机，包括检测内容中的思考标签 */}
        {(hasThinking || isThinkingAnyway) && (
          <ThinkingMode
            content={cleanContent}
            isExpanded={isThinkingExpanded}
            onToggleExpand={() => setIsThinkingExpanded(!isThinkingExpanded)}
            defaultHidden={false}
          />
        )}
        
        {/* 主要内容（移除思考标签后的内容） */}
        {contentWithoutThinking && (
          <div className="whitespace-pre-wrap">{renderImageIfUrl(contentWithoutThinking)}</div>
        )}
      </div>
    );
  };

  // 格式化时间（毫秒转秒）
  const formatDuration = (nanoseconds?: number) => {
    if (!nanoseconds) return null;
    const seconds = (nanoseconds / 1000000000).toFixed(2);
    return `${seconds}s`;
  };

  const renderGenerationStatsIcon = () => {
    // 根据是否有统计数据显示不同的悬浮内容
    const statsText = message.total_duration 
       ? `生成时间: ${(message.total_duration / 1000000).toFixed(2)}ms\n` +
         `提示词处理: ${message.prompt_eval_count || 0} tokens\n` +
         `生成内容: ${message.eval_count || 0} tokens\n` +
         `提示词速度: ${message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0} tokens/s\n` +
         `生成速度: ${message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0} tokens/s`
       : '正在生成中，统计信息将在完成后显示...';

    return (
      <div className="relative group">
        <Info className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help" />
        <div className="absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max">
          {statsText}
        </div>
      </div>
    );
  };

  // 渲染生成统计信息
  const renderGenerationStats = () => {
    if (!isAssistant || !message.total_duration) return null;

    return (
      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1">
        <div className="flex flex-wrap gap-4">
          <span>总时长: {formatDuration(message.total_duration)}</span>
          <span>加载时长: {formatDuration(message.load_duration)}</span>
        </div>
        <div className="flex flex-wrap gap-4">
          <span>提示评估: {message.prompt_eval_count} tokens ({formatDuration(message.prompt_eval_duration)})</span>
          <span>生成: {message.eval_count} tokens ({formatDuration(message.eval_duration)})</span>
        </div>
      </div>
    );
  };

  // 对于助手消息，检查是否应该显示
  if (isAssistant) {
    const assistantContent = renderAssistantContent(message.content);
    if (!assistantContent) {
      return null; // 不渲染空的助手消息
    }
  }

  return (
    <div className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && (
        <div className="flex flex-col items-start">
          <div className="flex items-start gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
              isTool ? 'bg-orange-600' : isToolCall ? 'bg-purple-600' : 'bg-blue-600'
            }`}>
              {isTool ? (
                <Wrench className="w-4 h-4 text-white" />
              ) : isToolCall ? (
                <Wrench className="w-4 h-4 text-white" />
              ) : (
                <Bot className="w-4 h-4 text-white" />
              )}
            </div>
            {/* AI状态指示器显示在头像右侧 */}
            {showAIStatus && aiState && aiState.status === 'loading' && (
              <div className="mt-1">
                <AIStatusIndicator aiState={aiState} />
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="flex flex-col max-w-[70%]">
        {/* 模型名称和统计图标显示在消息气泡上方 */}
        {!isUser && isAssistant && (
          <div className="flex items-center gap-2 mb-1">
            {/* 模型名称显示，如果没有则显示加载中 */}
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {message.model || '加载中...'}
            </span>
            {/* 统计信息图标始终显示，但悬浮内容根据数据可用性决定 */}
            {renderGenerationStatsIcon()}
          </div>
        )}
        <div className={`rounded-lg px-4 py-2 transition-colors duration-300 ${
        isUser
          ? 'bg-theme-primary text-white'
          : isTool
          ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700'
          : isToolCall
          ? 'bg-theme-background-tertiary border border-theme-border text-theme-foreground'
          : 'bg-theme-card border border-theme-border text-theme-foreground'
      }`}>
          {/* 移除工具调用loading状态显示，现在由独立的ToolCallMessage组件处理 */}

          {isToolCall ? renderToolCallContent() :
           isTool ? renderToolContent(message.content) :
           isAssistant ? renderAssistantContent(message.content) :
           <div className="whitespace-pre-wrap">{renderImageIfUrl(message.content)}</div>}
        </div>
      </div>
      
      {isUser && (
        <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          <User className="w-4 h-4 text-white" />
        </div>
      )}
    </div>
  );
}