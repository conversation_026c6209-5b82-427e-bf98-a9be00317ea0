'use client';

import React, { useRef, useEffect } from 'react';
import { Send, Square } from 'lucide-react';
import { ToolSettings } from '@/app/chat/components/ToolSettings';

interface ChatInputProps {
  inputMessage: string;
  isStreaming: boolean;
  selectedModel: string;
  enableTools: boolean;
  selectedTools: string[];
  onInputChange: (message: string) => void;
  onSendMessage: () => void;
  onStopGeneration: () => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: string[]) => void;
  onClearChat?: () => void;
}

export function ChatInput({
  inputMessage,
  isStreaming,
  selectedModel,
  enableTools,
  selectedTools,
  onInputChange,
  onSendMessage,
  onStopGeneration,
  onKeyPress,
  onToolsToggle,
  onSelectedToolsChange,
  onClearChat,
}: ChatInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 自动调整textarea高度
  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const lineHeight = 24; // 大约每行24px
      const maxHeight = lineHeight * 6; // 最大6行
      const minHeight = lineHeight * 1; // 最小1行
      
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      textarea.style.height = `${newHeight}px`;
      
      // 如果内容超过最大高度，启用滚动
      if (scrollHeight > maxHeight) {
        textarea.style.overflowY = 'auto';
      } else {
        textarea.style.overflowY = 'hidden';
      }
    }
  };

  // 当输入内容变化时调整高度
  useEffect(() => {
    adjustHeight();
  }, [inputMessage]);

  // 当输入框被清空时（发送消息后）自动聚焦
  useEffect(() => {
    if (inputMessage === '' && textareaRef.current) {
      // 使用setTimeout确保DOM更新完成后再聚焦
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 50);
    }
  }, [inputMessage]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onInputChange(e.target.value);
  };

  return (
    <>
      <ToolSettings
        selectedModel={selectedModel}
        enableTools={enableTools}
        selectedTools={selectedTools}
        onToolsToggle={onToolsToggle}
        onSelectedToolsChange={onSelectedToolsChange}
        onClearChat={onClearChat}
      />
      
      <div className="flex gap-3 items-end">
        <textarea
          ref={textareaRef}
          value={inputMessage}
          onChange={handleInputChange}
          onKeyPress={onKeyPress}
          placeholder="输入消息..."
          className="flex-1 p-3 border border-theme-input-border rounded-lg resize-none bg-theme-input text-theme-foreground placeholder-theme-foreground-muted focus:ring-2 focus:ring-theme-input-focus focus:border-transparent transition-colors duration-200"
          style={{ minHeight: '24px', lineHeight: '24px' }}
          rows={1}
        />
        <button
          onClick={isStreaming ? onStopGeneration : onSendMessage}
          disabled={!isStreaming && !inputMessage.trim()}
          className={`relative w-12 h-12 text-white rounded-full flex items-center justify-center transition-all duration-200 ${
            isStreaming
              ? 'bg-theme-error hover:bg-red-700'
              : 'bg-theme-primary hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed'
          } ${
            isStreaming ? 'before:content-[\"\"] before:absolute before:inset-0 before:rounded-full before:border-2 before:border-transparent before:border-t-white before:border-r-white before:animate-spin' : ''
          }`}
        >
          {isStreaming ? (
            <Square className="w-4 h-4 relative z-10" />
          ) : (
            <Send className="w-5 h-5" />
          )}
        </button>
      </div>
    </>
  );
}