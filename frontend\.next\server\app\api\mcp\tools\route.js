/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/tools/route";
exports.ids = ["app/api/mcp/tools/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_mcp_tools_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mcp/tools/route.ts */ \"(rsc)/./src/app/api/mcp/tools/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/tools/route\",\n        pathname: \"/api/mcp/tools\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/tools/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\mcp\\\\tools\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_mcp_tools_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/mcp/tools/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/mcp/tools/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mcp_mcp_multi_server_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mcp/mcp-multi-server-client */ \"(rsc)/./src/lib/mcp/mcp-multi-server-client.ts\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_3__.join(process.cwd(), 'chat.db');\n// 从数据库获取服务器配置\nfunction getServersFromDatabase() {\n    const db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_2___default())(dbPath);\n    try {\n        const servers = db.prepare('SELECT * FROM mcp_servers WHERE enabled = 1').all();\n        const config = {};\n        servers.forEach((server)=>{\n            const serverConfig = {\n                type: server.type,\n                command: server.command,\n                args: server.args ? JSON.parse(server.args) : [],\n                env: server.env ? JSON.parse(server.env) : {}\n            };\n            // 添加SSE/HTTP相关配置\n            if (server.url) {\n                serverConfig.url = server.url;\n            }\n            if (server.tools) {\n                serverConfig.tools = JSON.parse(server.tools);\n            }\n            config[server.name] = serverConfig;\n        });\n        return config;\n    } finally{\n        db.close();\n    }\n}\n// 保存工具到数据库\nasync function saveToolsToDatabase(tools) {\n    const db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_2___default())(dbPath);\n    try {\n        // 获取服务器ID映射\n        const servers = db.prepare('SELECT id, name FROM mcp_servers WHERE enabled = 1').all();\n        const serverMap = new Map();\n        servers.forEach((server)=>{\n            serverMap.set(server.name, server.id);\n        });\n        // 为每个工具保存到数据库\n        for (const tool of tools){\n            const serverId = serverMap.get(tool.serverName);\n            if (serverId) {\n                // 检查工具是否已存在\n                const existingTool = db.prepare('SELECT id FROM mcp_tools WHERE server_id = ? AND name = ?').get(serverId, tool.name);\n                if (existingTool) {\n                    // 更新现有工具\n                    const result = db.prepare(`\n            UPDATE mcp_tools\n            SET description = ?, input_schema = ?, is_available = ?, updated_at = datetime('now')\n            WHERE id = ?\n          `).run(tool.description || null, JSON.stringify(tool.inputSchema), true, existingTool.id);\n                } else {\n                    // 插入新工具\n                    db.prepare(`\n            INSERT INTO mcp_tools (server_id, name, description, input_schema, is_available, enabled, created_at, updated_at)\n            VALUES (?, ?, ?, ?, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)\n          `).run(serverId, tool.name, tool.description || '', JSON.stringify(tool.inputSchema || {}));\n                }\n            }\n        }\n        console.log(`已保存 ${tools.length} 个工具到数据库`);\n    } catch (error) {\n        console.error('保存工具到数据库失败:', error);\n    } finally{\n        db.close();\n    }\n}\n// 从数据库获取工具列表\nfunction getToolsFromDatabase(serverName, includeDisabled = false) {\n    const db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_2___default())(dbPath);\n    try {\n        let query = `\n      SELECT \n        t.id,\n        t.server_id,\n        t.name,\n        t.description,\n        t.input_schema,\n        t.is_available,\n        t.enabled,\n        s.name as server_name,\n        s.type as server_type\n      FROM mcp_tools t\n      JOIN mcp_servers s ON t.server_id = s.id\n      WHERE t.is_available = 1 AND s.enabled = 1\n    `;\n        // 如果不包含禁用的工具，则添加enabled过滤条件\n        if (!includeDisabled) {\n            query += ' AND t.enabled = 1';\n        }\n        const params = [];\n        if (serverName) {\n            query += ' AND s.name = ?';\n            params.push(serverName);\n        }\n        query += ' ORDER BY s.name, t.name';\n        const tools = db.prepare(query).all(...params);\n        return tools.map((tool)=>{\n            // 解析inputSchema并清理$schema字段以符合Ollama要求\n            let inputSchema = {};\n            if (tool.input_schema) {\n                try {\n                    inputSchema = JSON.parse(tool.input_schema);\n                    if (inputSchema && typeof inputSchema === 'object' && '$schema' in inputSchema) {\n                        delete inputSchema.$schema;\n                    }\n                } catch (error) {\n                    console.error('解析工具inputSchema失败:', error);\n                    inputSchema = {};\n                }\n            }\n            return {\n                id: tool.id,\n                server_id: tool.server_id,\n                name: tool.name,\n                description: tool.description,\n                input_schema: tool.input_schema,\n                inputSchema,\n                is_available: Boolean(tool.is_available),\n                enabled: Boolean(tool.enabled),\n                usage_count: 0,\n                created_at: '',\n                updated_at: '',\n                server_name: tool.server_name,\n                serverName: tool.server_name,\n                serverType: tool.server_type\n            };\n        });\n    } finally{\n        db.close();\n    }\n}\n/**\n * 获取MCP工具列表API\n * GET /api/mcp/tools?server=xxx&refresh=true\n */ async function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const serverName = searchParams.get('server'); // 指定服务器名称\n        const refresh = searchParams.get('refresh') === 'true'; // 是否强制刷新\n        const includeDisabled = searchParams.get('includeDisabled') === 'true'; // 是否包含禁用的工具\n        let tools = [];\n        // 优先从数据库获取工具（除非强制刷新）\n        if (!refresh) {\n            tools = getToolsFromDatabase(serverName ?? undefined, includeDisabled);\n            // 直接返回数据库中的工具，不再连接外部服务器\n            console.log(`从数据库获取到 ${tools.length} 个工具`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                tools: tools,\n                fromCache: true\n            });\n        }\n        // 如果数据库中没有数据或强制刷新，从服务器获取\n        console.log('从服务器获取工具列表...');\n        // 获取本地MCP工具\n        if (!serverName || serverName === 'local') {\n            try {\n                const { mcpServerClient } = __webpack_require__(/*! ../../../../lib/mcp/mcp-client-server */ \"(rsc)/./src/lib/mcp/mcp-client-server.ts\");\n                if (mcpServerClient.isClientConnected()) {\n                    const localTools = mcpServerClient.getAvailableTools();\n                    tools.push(...localTools.map((tool)=>({\n                            ...tool,\n                            serverName: 'local',\n                            serverType: 'stdio'\n                        })));\n                }\n            } catch (error) {\n                console.error('获取本地MCP工具失败:', error);\n            }\n        }\n        // 获取外部服务器MCP工具\n        if (!serverName || serverName !== 'local') {\n            try {\n                // 从数据库获取服务器配置\n                const config = getServersFromDatabase();\n                // 如果指定了服务器名称，只获取该服务器的配置\n                const targetConfig = {};\n                if (serverName && serverName !== 'local') {\n                    if (config[serverName]) {\n                        targetConfig[serverName] = config[serverName];\n                    }\n                } else if (!serverName) {\n                    Object.assign(targetConfig, config);\n                }\n                if (Object.keys(targetConfig).length > 0) {\n                    console.log('MCP服务器配置:', JSON.stringify(targetConfig, null, 2));\n                    // 设置配置并确保连接\n                    _lib_mcp_mcp_multi_server_client__WEBPACK_IMPORTED_MODULE_1__.multiServerMcpClient.setConfig(targetConfig);\n                    console.log('开始连接所有MCP服务器...');\n                    await _lib_mcp_mcp_multi_server_client__WEBPACK_IMPORTED_MODULE_1__.multiServerMcpClient.connectAll();\n                    // 获取连接状态\n                    const connectionStatus = _lib_mcp_mcp_multi_server_client__WEBPACK_IMPORTED_MODULE_1__.multiServerMcpClient.getConnectionStatus();\n                    console.log('MCP服务器连接状态:', connectionStatus);\n                    // 刷新所有工具\n                    console.log('开始刷新所有工具...');\n                    await _lib_mcp_mcp_multi_server_client__WEBPACK_IMPORTED_MODULE_1__.multiServerMcpClient.refreshAllTools();\n                    // 获取所有工具列表\n                    const multiServerTools = _lib_mcp_mcp_multi_server_client__WEBPACK_IMPORTED_MODULE_1__.multiServerMcpClient.getAllAvailableTools();\n                    console.log(`获取到 ${multiServerTools.length} 个多服务器MCP工具:`, multiServerTools.map((t)=>({\n                            name: t.name,\n                            server: t.serverName\n                        })));\n                    // 保存工具到数据库\n                    await saveToolsToDatabase(multiServerTools);\n                    // 过滤指定服务器的工具\n                    if (serverName) {\n                        tools.push(...multiServerTools.filter((tool)=>tool.serverName === serverName));\n                    } else {\n                        tools.push(...multiServerTools);\n                    }\n                }\n            } catch (error) {\n                console.error('获取多服务器MCP工具失败:', error);\n            }\n        }\n        console.log(`总共返回 ${tools.length} 个工具`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            tools: tools,\n            fromCache: false\n        });\n    } catch (error) {\n        console.error('获取MCP工具列表失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '获取工具列表失败'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9tY3AvdG9vbHMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEyQztBQUM4QjtBQUNuQztBQUNUO0FBRTdCLE1BQU1JLFNBQVNELHNDQUFTLENBQUNHLFFBQVFDLEdBQUcsSUFBSTtBQUV4QyxjQUFjO0FBQ2QsU0FBU0M7SUFDUCxNQUFNQyxLQUFLLElBQUlQLHVEQUFRQSxDQUFDRTtJQUN4QixJQUFJO1FBQ0YsTUFBTU0sVUFBVUQsR0FBR0UsT0FBTyxDQUFDLCtDQUErQ0MsR0FBRztRQUM3RSxNQUFNQyxTQUFjLENBQUM7UUFFckJILFFBQVFJLE9BQU8sQ0FBQyxDQUFDQztZQUNmLE1BQU1DLGVBQW9CO2dCQUN4QkMsTUFBTUYsT0FBT0UsSUFBSTtnQkFDakJDLFNBQVNILE9BQU9HLE9BQU87Z0JBQ3ZCQyxNQUFNSixPQUFPSSxJQUFJLEdBQUdDLEtBQUtDLEtBQUssQ0FBQ04sT0FBT0ksSUFBSSxJQUFJLEVBQUU7Z0JBQ2hERyxLQUFLUCxPQUFPTyxHQUFHLEdBQUdGLEtBQUtDLEtBQUssQ0FBQ04sT0FBT08sR0FBRyxJQUFJLENBQUM7WUFDOUM7WUFFQSxpQkFBaUI7WUFDakIsSUFBSVAsT0FBT1EsR0FBRyxFQUFFO2dCQUNkUCxhQUFhTyxHQUFHLEdBQUdSLE9BQU9RLEdBQUc7WUFDL0I7WUFFQSxJQUFJUixPQUFPUyxLQUFLLEVBQUU7Z0JBQ2hCUixhQUFhUSxLQUFLLEdBQUdKLEtBQUtDLEtBQUssQ0FBQ04sT0FBT1MsS0FBSztZQUM5QztZQUVBWCxNQUFNLENBQUNFLE9BQU9VLElBQUksQ0FBQyxHQUFHVDtRQUN4QjtRQUVBLE9BQU9IO0lBQ1QsU0FBVTtRQUNSSixHQUFHaUIsS0FBSztJQUNWO0FBQ0Y7QUFFQSxXQUFXO0FBQ1gsZUFBZUMsb0JBQW9CSCxLQUFZO0lBQzdDLE1BQU1mLEtBQUssSUFBSVAsdURBQVFBLENBQUNFO0lBQ3hCLElBQUk7UUFDRixZQUFZO1FBQ1osTUFBTU0sVUFBVUQsR0FBR0UsT0FBTyxDQUFDLHNEQUFzREMsR0FBRztRQUNwRixNQUFNZ0IsWUFBWSxJQUFJQztRQUN0Qm5CLFFBQVFJLE9BQU8sQ0FBQyxDQUFDQztZQUNmYSxVQUFVRSxHQUFHLENBQUNmLE9BQU9VLElBQUksRUFBRVYsT0FBT2dCLEVBQUU7UUFDdEM7UUFFQSxjQUFjO1FBQ2QsS0FBSyxNQUFNQyxRQUFRUixNQUFPO1lBQ3hCLE1BQU1TLFdBQVdMLFVBQVVNLEdBQUcsQ0FBQ0YsS0FBS0csVUFBVTtZQUM5QyxJQUFJRixVQUFVO2dCQUNaLFlBQVk7Z0JBQ1osTUFBTUcsZUFBZTNCLEdBQUdFLE9BQU8sQ0FBQyw2REFBNkR1QixHQUFHLENBQUNELFVBQVVELEtBQUtQLElBQUk7Z0JBRXBILElBQUlXLGNBQWM7b0JBQ2hCLFNBQVM7b0JBQ1QsTUFBTUMsU0FBUzVCLEdBQUdFLE9BQU8sQ0FBQyxDQUFDOzs7O1VBSTNCLENBQUMsRUFBRTJCLEdBQUcsQ0FBQ04sS0FBS08sV0FBVyxJQUFJLE1BQU1uQixLQUFLb0IsU0FBUyxDQUFDUixLQUFLUyxXQUFXLEdBQUcsTUFBTUwsYUFBYUwsRUFBRTtnQkFDMUYsT0FBTztvQkFDTCxRQUFRO29CQUNSdEIsR0FBR0UsT0FBTyxDQUFDLENBQUM7OztVQUdaLENBQUMsRUFBRTJCLEdBQUcsQ0FBQ0wsVUFBVUQsS0FBS1AsSUFBSSxFQUFFTyxLQUFLTyxXQUFXLElBQUksSUFBSW5CLEtBQUtvQixTQUFTLENBQUNSLEtBQUtTLFdBQVcsSUFBSSxDQUFDO2dCQUMxRjtZQUNGO1FBQ0Y7UUFFQUMsUUFBUUMsR0FBRyxDQUFDLENBQUMsSUFBSSxFQUFFbkIsTUFBTW9CLE1BQU0sQ0FBQyxRQUFRLENBQUM7SUFDM0MsRUFBRSxPQUFPQyxPQUFPO1FBQ2RILFFBQVFHLEtBQUssQ0FBQyxlQUFlQTtJQUMvQixTQUFVO1FBQ1JwQyxHQUFHaUIsS0FBSztJQUNWO0FBQ0Y7QUFFQSxhQUFhO0FBQ2IsU0FBU29CLHFCQUFxQlgsVUFBbUIsRUFBRVksa0JBQWtCLEtBQUs7SUFDeEUsTUFBTXRDLEtBQUssSUFBSVAsdURBQVFBLENBQUNFO0lBQ3hCLElBQUk7UUFDRixJQUFJNEMsUUFBUSxDQUFDOzs7Ozs7Ozs7Ozs7OztJQWNiLENBQUM7UUFFRCw0QkFBNEI7UUFDNUIsSUFBSSxDQUFDRCxpQkFBaUI7WUFDcEJDLFNBQVM7UUFDWDtRQUVBLE1BQU1DLFNBQWdCLEVBQUU7UUFDeEIsSUFBSWQsWUFBWTtZQUNkYSxTQUFTO1lBQ1RDLE9BQU9DLElBQUksQ0FBQ2Y7UUFDZDtRQUVBYSxTQUFTO1FBRVQsTUFBTXhCLFFBQVFmLEdBQUdFLE9BQU8sQ0FBQ3FDLE9BQU9wQyxHQUFHLElBQUlxQztRQUV2QyxPQUFPekIsTUFBTTJCLEdBQUcsQ0FBQyxDQUFDbkI7WUFDaEIsdUNBQXVDO1lBQ3RDLElBQUlTLGNBQW1CLENBQUM7WUFDeEIsSUFBSVQsS0FBS29CLFlBQVksRUFBRTtnQkFDckIsSUFBSTtvQkFDRlgsY0FBY3JCLEtBQUtDLEtBQUssQ0FBQ1csS0FBS29CLFlBQVk7b0JBQzFDLElBQUlYLGVBQWUsT0FBT0EsZ0JBQWdCLFlBQVksYUFBYUEsYUFBYTt3QkFDOUUsT0FBT0EsWUFBWVksT0FBTztvQkFDNUI7Z0JBQ0YsRUFBRSxPQUFPUixPQUFPO29CQUNkSCxRQUFRRyxLQUFLLENBQUMsc0JBQXNCQTtvQkFDcENKLGNBQWMsQ0FBQztnQkFDakI7WUFDRjtZQUVELE9BQU87Z0JBQ0xWLElBQUlDLEtBQUtELEVBQUU7Z0JBQ1h1QixXQUFXdEIsS0FBS3NCLFNBQVM7Z0JBQ3pCN0IsTUFBTU8sS0FBS1AsSUFBSTtnQkFDZmMsYUFBYVAsS0FBS08sV0FBVztnQkFDN0JhLGNBQWNwQixLQUFLb0IsWUFBWTtnQkFDL0JYO2dCQUNBYyxjQUFjQyxRQUFReEIsS0FBS3VCLFlBQVk7Z0JBQ3ZDRSxTQUFTRCxRQUFReEIsS0FBS3lCLE9BQU87Z0JBQzdCQyxhQUFhO2dCQUNiQyxZQUFZO2dCQUNaQyxZQUFZO2dCQUNaQyxhQUFhN0IsS0FBSzZCLFdBQVc7Z0JBQzdCMUIsWUFBWUgsS0FBSzZCLFdBQVc7Z0JBQzVCQyxZQUFZOUIsS0FBSytCLFdBQVc7WUFDOUI7UUFDRjtJQUNGLFNBQVU7UUFDUnRELEdBQUdpQixLQUFLO0lBQ1Y7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVzQyxJQUFJQyxPQUFnQjtJQUN4QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJRixRQUFRMUMsR0FBRztRQUM1QyxNQUFNWSxhQUFhK0IsYUFBYWhDLEdBQUcsQ0FBQyxXQUFXLFVBQVU7UUFDekQsTUFBTWtDLFVBQVVGLGFBQWFoQyxHQUFHLENBQUMsZUFBZSxRQUFRLFNBQVM7UUFDakUsTUFBTWEsa0JBQWtCbUIsYUFBYWhDLEdBQUcsQ0FBQyx1QkFBdUIsUUFBUSxZQUFZO1FBRXBGLElBQUlWLFFBQWUsRUFBRTtRQUVyQixxQkFBcUI7UUFDckIsSUFBSSxDQUFDNEMsU0FBUztZQUNaNUMsUUFBUXNCLHFCQUFxQlgsY0FBY2tDLFdBQVd0QjtZQUV0RCx3QkFBd0I7WUFDeEJMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLFFBQVEsRUFBRW5CLE1BQU1vQixNQUFNLENBQUMsSUFBSSxDQUFDO1lBQ3pDLE9BQU81QyxxREFBWUEsQ0FBQ3NFLElBQUksQ0FBQztnQkFDdkJDLFNBQVM7Z0JBQ1QvQyxPQUFPQTtnQkFDUGdELFdBQVc7WUFDYjtRQUNGO1FBRUEseUJBQXlCO1FBQ3pCOUIsUUFBUUMsR0FBRyxDQUFDO1FBRVosWUFBWTtRQUNaLElBQUksQ0FBQ1IsY0FBY0EsZUFBZSxTQUFTO1lBQ3pDLElBQUk7Z0JBQ0YsTUFBTSxFQUFFc0MsZUFBZSxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDLHVGQUF1QztnQkFDM0UsSUFBSUQsZ0JBQWdCRSxpQkFBaUIsSUFBSTtvQkFDdkMsTUFBTUMsYUFBYUgsZ0JBQWdCSSxpQkFBaUI7b0JBQ3BEckQsTUFBTTBCLElBQUksSUFBSTBCLFdBQVd6QixHQUFHLENBQUMsQ0FBQ25CLE9BQWU7NEJBQzNDLEdBQUdBLElBQUk7NEJBQ1BHLFlBQVk7NEJBQ1oyQixZQUFZO3dCQUNkO2dCQUNGO1lBQ0YsRUFBRSxPQUFPakIsT0FBTztnQkFDZEgsUUFBUUcsS0FBSyxDQUFDLGdCQUFnQkE7WUFDaEM7UUFDRjtRQUVBLGVBQWU7UUFDZixJQUFJLENBQUNWLGNBQWNBLGVBQWUsU0FBUztZQUN6QyxJQUFJO2dCQUNGLGNBQWM7Z0JBQ2QsTUFBTXRCLFNBQVNMO2dCQUVmLHdCQUF3QjtnQkFDeEIsTUFBTXNFLGVBQW9CLENBQUM7Z0JBQzNCLElBQUkzQyxjQUFjQSxlQUFlLFNBQVM7b0JBQ3hDLElBQUl0QixNQUFNLENBQUNzQixXQUFXLEVBQUU7d0JBQ3RCMkMsWUFBWSxDQUFDM0MsV0FBVyxHQUFHdEIsTUFBTSxDQUFDc0IsV0FBVztvQkFDL0M7Z0JBQ0YsT0FBTyxJQUFJLENBQUNBLFlBQVk7b0JBQ3RCNEMsT0FBT0MsTUFBTSxDQUFDRixjQUFjakU7Z0JBQzlCO2dCQUVBLElBQUlrRSxPQUFPRSxJQUFJLENBQUNILGNBQWNsQyxNQUFNLEdBQUcsR0FBRztvQkFDeENGLFFBQVFDLEdBQUcsQ0FBQyxhQUFhdkIsS0FBS29CLFNBQVMsQ0FBQ3NDLGNBQWMsTUFBTTtvQkFFNUQsWUFBWTtvQkFDWjdFLGtGQUFvQkEsQ0FBQ2lGLFNBQVMsQ0FBQ0o7b0JBQy9CcEMsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE1BQU0xQyxrRkFBb0JBLENBQUNrRixVQUFVO29CQUVyQyxTQUFTO29CQUNULE1BQU1DLG1CQUFtQm5GLGtGQUFvQkEsQ0FBQ29GLG1CQUFtQjtvQkFDakUzQyxRQUFRQyxHQUFHLENBQUMsZUFBZXlDO29CQUUzQixTQUFTO29CQUNUMUMsUUFBUUMsR0FBRyxDQUFDO29CQUNaLE1BQU0xQyxrRkFBb0JBLENBQUNxRixlQUFlO29CQUUxQyxXQUFXO29CQUNYLE1BQU1DLG1CQUFtQnRGLGtGQUFvQkEsQ0FBQ3VGLG9CQUFvQjtvQkFDbEU5QyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxJQUFJLEVBQUU0QyxpQkFBaUIzQyxNQUFNLENBQUMsWUFBWSxDQUFDLEVBQUUyQyxpQkFBaUJwQyxHQUFHLENBQUNzQyxDQUFBQSxJQUFNOzRCQUFFaEUsTUFBTWdFLEVBQUVoRSxJQUFJOzRCQUFFVixRQUFRMEUsRUFBRXRELFVBQVU7d0JBQUM7b0JBRTFILFdBQVc7b0JBQ1gsTUFBTVIsb0JBQW9CNEQ7b0JBRTFCLGFBQWE7b0JBQ2IsSUFBSXBELFlBQVk7d0JBQ2RYLE1BQU0wQixJQUFJLElBQUlxQyxpQkFBaUJHLE1BQU0sQ0FBQzFELENBQUFBLE9BQVFBLEtBQUtHLFVBQVUsS0FBS0E7b0JBQ3BFLE9BQU87d0JBQ0xYLE1BQU0wQixJQUFJLElBQUlxQztvQkFDaEI7Z0JBQ0Y7WUFDRixFQUFFLE9BQU8xQyxPQUFPO2dCQUNkSCxRQUFRRyxLQUFLLENBQUMsa0JBQWtCQTtZQUNsQztRQUNGO1FBRUFILFFBQVFDLEdBQUcsQ0FBQyxDQUFDLEtBQUssRUFBRW5CLE1BQU1vQixNQUFNLENBQUMsSUFBSSxDQUFDO1FBQ3RDLE9BQU81QyxxREFBWUEsQ0FBQ3NFLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUL0MsT0FBT0E7WUFDUGdELFdBQVc7UUFDYjtJQUNGLEVBQUUsT0FBTzNCLE9BQU87UUFDZEgsUUFBUUcsS0FBSyxDQUFDLGdCQUFnQkE7UUFDOUIsT0FBTzdDLHFEQUFZQSxDQUFDc0UsSUFBSSxDQUN0QjtZQUFFekIsT0FBTztRQUFXLEdBQ3BCO1lBQUU4QyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxhcHBcXGFwaVxcbWNwXFx0b29sc1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgbXVsdGlTZXJ2ZXJNY3BDbGllbnQgfSBmcm9tICdAL2xpYi9tY3AvbWNwLW11bHRpLXNlcnZlci1jbGllbnQnO1xuaW1wb3J0IERhdGFiYXNlIGZyb20gJ2JldHRlci1zcWxpdGUzJztcbmltcG9ydCAqIGFzIHBhdGggZnJvbSAncGF0aCc7XG5cbmNvbnN0IGRiUGF0aCA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCAnY2hhdC5kYicpO1xuXG4vLyDku47mlbDmja7lupPojrflj5bmnI3liqHlmajphY3nva5cbmZ1bmN0aW9uIGdldFNlcnZlcnNGcm9tRGF0YWJhc2UoKSB7XG4gIGNvbnN0IGRiID0gbmV3IERhdGFiYXNlKGRiUGF0aCk7XG4gIHRyeSB7XG4gICAgY29uc3Qgc2VydmVycyA9IGRiLnByZXBhcmUoJ1NFTEVDVCAqIEZST00gbWNwX3NlcnZlcnMgV0hFUkUgZW5hYmxlZCA9IDEnKS5hbGwoKTtcbiAgICBjb25zdCBjb25maWc6IGFueSA9IHt9O1xuICAgIFxuICAgIHNlcnZlcnMuZm9yRWFjaCgoc2VydmVyOiBhbnkpID0+IHtcbiAgICAgIGNvbnN0IHNlcnZlckNvbmZpZzogYW55ID0ge1xuICAgICAgICB0eXBlOiBzZXJ2ZXIudHlwZSxcbiAgICAgICAgY29tbWFuZDogc2VydmVyLmNvbW1hbmQsXG4gICAgICAgIGFyZ3M6IHNlcnZlci5hcmdzID8gSlNPTi5wYXJzZShzZXJ2ZXIuYXJncykgOiBbXSxcbiAgICAgICAgZW52OiBzZXJ2ZXIuZW52ID8gSlNPTi5wYXJzZShzZXJ2ZXIuZW52KSA6IHt9XG4gICAgICB9O1xuICAgICAgXG4gICAgICAvLyDmt7vliqBTU0UvSFRUUOebuOWFs+mFjee9rlxuICAgICAgaWYgKHNlcnZlci51cmwpIHtcbiAgICAgICAgc2VydmVyQ29uZmlnLnVybCA9IHNlcnZlci51cmw7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGlmIChzZXJ2ZXIudG9vbHMpIHtcbiAgICAgICAgc2VydmVyQ29uZmlnLnRvb2xzID0gSlNPTi5wYXJzZShzZXJ2ZXIudG9vbHMpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25maWdbc2VydmVyLm5hbWVdID0gc2VydmVyQ29uZmlnO1xuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiBjb25maWc7XG4gIH0gZmluYWxseSB7XG4gICAgZGIuY2xvc2UoKTtcbiAgfVxufVxuXG4vLyDkv53lrZjlt6XlhbfliLDmlbDmja7lupNcbmFzeW5jIGZ1bmN0aW9uIHNhdmVUb29sc1RvRGF0YWJhc2UodG9vbHM6IGFueVtdKSB7XG4gIGNvbnN0IGRiID0gbmV3IERhdGFiYXNlKGRiUGF0aCk7XG4gIHRyeSB7XG4gICAgLy8g6I635Y+W5pyN5Yqh5ZmoSUTmmKDlsIRcbiAgICBjb25zdCBzZXJ2ZXJzID0gZGIucHJlcGFyZSgnU0VMRUNUIGlkLCBuYW1lIEZST00gbWNwX3NlcnZlcnMgV0hFUkUgZW5hYmxlZCA9IDEnKS5hbGwoKTtcbiAgICBjb25zdCBzZXJ2ZXJNYXAgPSBuZXcgTWFwKCk7XG4gICAgc2VydmVycy5mb3JFYWNoKChzZXJ2ZXI6IGFueSkgPT4ge1xuICAgICAgc2VydmVyTWFwLnNldChzZXJ2ZXIubmFtZSwgc2VydmVyLmlkKTtcbiAgICB9KTtcbiAgICBcbiAgICAvLyDkuLrmr4/kuKrlt6Xlhbfkv53lrZjliLDmlbDmja7lupNcbiAgICBmb3IgKGNvbnN0IHRvb2wgb2YgdG9vbHMpIHtcbiAgICAgIGNvbnN0IHNlcnZlcklkID0gc2VydmVyTWFwLmdldCh0b29sLnNlcnZlck5hbWUpO1xuICAgICAgaWYgKHNlcnZlcklkKSB7XG4gICAgICAgIC8vIOajgOafpeW3peWFt+aYr+WQpuW3suWtmOWcqFxuICAgICAgICBjb25zdCBleGlzdGluZ1Rvb2wgPSBkYi5wcmVwYXJlKCdTRUxFQ1QgaWQgRlJPTSBtY3BfdG9vbHMgV0hFUkUgc2VydmVyX2lkID0gPyBBTkQgbmFtZSA9ID8nKS5nZXQoc2VydmVySWQsIHRvb2wubmFtZSkgYXMgeyBpZDogbnVtYmVyIH0gfCB1bmRlZmluZWQ7XG4gICAgICAgIFxuICAgICAgICBpZiAoZXhpc3RpbmdUb29sKSB7XG4gICAgICAgICAgLy8g5pu05paw546w5pyJ5bel5YW3XG4gICAgICAgICAgY29uc3QgcmVzdWx0ID0gZGIucHJlcGFyZShgXG4gICAgICAgICAgICBVUERBVEUgbWNwX3Rvb2xzXG4gICAgICAgICAgICBTRVQgZGVzY3JpcHRpb24gPSA/LCBpbnB1dF9zY2hlbWEgPSA/LCBpc19hdmFpbGFibGUgPSA/LCB1cGRhdGVkX2F0ID0gZGF0ZXRpbWUoJ25vdycpXG4gICAgICAgICAgICBXSEVSRSBpZCA9ID9cbiAgICAgICAgICBgKS5ydW4odG9vbC5kZXNjcmlwdGlvbiB8fCBudWxsLCBKU09OLnN0cmluZ2lmeSh0b29sLmlucHV0U2NoZW1hKSwgdHJ1ZSwgZXhpc3RpbmdUb29sLmlkKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyDmj5LlhaXmlrDlt6XlhbdcbiAgICAgICAgICBkYi5wcmVwYXJlKGBcbiAgICAgICAgICAgIElOU0VSVCBJTlRPIG1jcF90b29scyAoc2VydmVyX2lkLCBuYW1lLCBkZXNjcmlwdGlvbiwgaW5wdXRfc2NoZW1hLCBpc19hdmFpbGFibGUsIGVuYWJsZWQsIGNyZWF0ZWRfYXQsIHVwZGF0ZWRfYXQpXG4gICAgICAgICAgICBWQUxVRVMgKD8sID8sID8sID8sIDEsIDEsIENVUlJFTlRfVElNRVNUQU1QLCBDVVJSRU5UX1RJTUVTVEFNUClcbiAgICAgICAgICBgKS5ydW4oc2VydmVySWQsIHRvb2wubmFtZSwgdG9vbC5kZXNjcmlwdGlvbiB8fCAnJywgSlNPTi5zdHJpbmdpZnkodG9vbC5pbnB1dFNjaGVtYSB8fCB7fSkpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIGNvbnNvbGUubG9nKGDlt7Lkv53lrZggJHt0b29scy5sZW5ndGh9IOS4quW3peWFt+WIsOaVsOaNruW6k2ApO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOW3peWFt+WIsOaVsOaNruW6k+Wksei0pTonLCBlcnJvcik7XG4gIH0gZmluYWxseSB7XG4gICAgZGIuY2xvc2UoKTtcbiAgfVxufVxuXG4vLyDku47mlbDmja7lupPojrflj5blt6XlhbfliJfooahcbmZ1bmN0aW9uIGdldFRvb2xzRnJvbURhdGFiYXNlKHNlcnZlck5hbWU/OiBzdHJpbmcsIGluY2x1ZGVEaXNhYmxlZCA9IGZhbHNlKSB7XG4gIGNvbnN0IGRiID0gbmV3IERhdGFiYXNlKGRiUGF0aCk7XG4gIHRyeSB7XG4gICAgbGV0IHF1ZXJ5ID0gYFxuICAgICAgU0VMRUNUIFxuICAgICAgICB0LmlkLFxuICAgICAgICB0LnNlcnZlcl9pZCxcbiAgICAgICAgdC5uYW1lLFxuICAgICAgICB0LmRlc2NyaXB0aW9uLFxuICAgICAgICB0LmlucHV0X3NjaGVtYSxcbiAgICAgICAgdC5pc19hdmFpbGFibGUsXG4gICAgICAgIHQuZW5hYmxlZCxcbiAgICAgICAgcy5uYW1lIGFzIHNlcnZlcl9uYW1lLFxuICAgICAgICBzLnR5cGUgYXMgc2VydmVyX3R5cGVcbiAgICAgIEZST00gbWNwX3Rvb2xzIHRcbiAgICAgIEpPSU4gbWNwX3NlcnZlcnMgcyBPTiB0LnNlcnZlcl9pZCA9IHMuaWRcbiAgICAgIFdIRVJFIHQuaXNfYXZhaWxhYmxlID0gMSBBTkQgcy5lbmFibGVkID0gMVxuICAgIGA7XG4gICAgXG4gICAgLy8g5aaC5p6c5LiN5YyF5ZCr56aB55So55qE5bel5YW377yM5YiZ5re75YqgZW5hYmxlZOi/h+a7pOadoeS7tlxuICAgIGlmICghaW5jbHVkZURpc2FibGVkKSB7XG4gICAgICBxdWVyeSArPSAnIEFORCB0LmVuYWJsZWQgPSAxJztcbiAgICB9XG4gICAgXG4gICAgY29uc3QgcGFyYW1zOiBhbnlbXSA9IFtdO1xuICAgIGlmIChzZXJ2ZXJOYW1lKSB7XG4gICAgICBxdWVyeSArPSAnIEFORCBzLm5hbWUgPSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKHNlcnZlck5hbWUpO1xuICAgIH1cbiAgICBcbiAgICBxdWVyeSArPSAnIE9SREVSIEJZIHMubmFtZSwgdC5uYW1lJztcbiAgICBcbiAgICBjb25zdCB0b29scyA9IGRiLnByZXBhcmUocXVlcnkpLmFsbCguLi5wYXJhbXMpO1xuICAgIFxuICAgIHJldHVybiB0b29scy5tYXAoKHRvb2w6IGFueSkgPT4ge1xuICAgICAgLy8g6Kej5p6QaW5wdXRTY2hlbWHlubbmuIXnkIYkc2NoZW1h5a2X5q615Lul56ym5ZCIT2xsYW1h6KaB5rGCXG4gICAgICAgbGV0IGlucHV0U2NoZW1hOiBhbnkgPSB7fTtcbiAgICAgICBpZiAodG9vbC5pbnB1dF9zY2hlbWEpIHtcbiAgICAgICAgIHRyeSB7XG4gICAgICAgICAgIGlucHV0U2NoZW1hID0gSlNPTi5wYXJzZSh0b29sLmlucHV0X3NjaGVtYSk7XG4gICAgICAgICAgIGlmIChpbnB1dFNjaGVtYSAmJiB0eXBlb2YgaW5wdXRTY2hlbWEgPT09ICdvYmplY3QnICYmICckc2NoZW1hJyBpbiBpbnB1dFNjaGVtYSkge1xuICAgICAgICAgICAgIGRlbGV0ZSBpbnB1dFNjaGVtYS4kc2NoZW1hO1xuICAgICAgICAgICB9XG4gICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDlt6XlhbdpbnB1dFNjaGVtYeWksei0pTonLCBlcnJvcik7XG4gICAgICAgICAgIGlucHV0U2NoZW1hID0ge307XG4gICAgICAgICB9XG4gICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4ge1xuICAgICAgICBpZDogdG9vbC5pZCxcbiAgICAgICAgc2VydmVyX2lkOiB0b29sLnNlcnZlcl9pZCxcbiAgICAgICAgbmFtZTogdG9vbC5uYW1lLFxuICAgICAgICBkZXNjcmlwdGlvbjogdG9vbC5kZXNjcmlwdGlvbixcbiAgICAgICAgaW5wdXRfc2NoZW1hOiB0b29sLmlucHV0X3NjaGVtYSxcbiAgICAgICAgaW5wdXRTY2hlbWEsXG4gICAgICAgIGlzX2F2YWlsYWJsZTogQm9vbGVhbih0b29sLmlzX2F2YWlsYWJsZSksXG4gICAgICAgIGVuYWJsZWQ6IEJvb2xlYW4odG9vbC5lbmFibGVkKSxcbiAgICAgICAgdXNhZ2VfY291bnQ6IDAsXG4gICAgICAgIGNyZWF0ZWRfYXQ6ICcnLFxuICAgICAgICB1cGRhdGVkX2F0OiAnJyxcbiAgICAgICAgc2VydmVyX25hbWU6IHRvb2wuc2VydmVyX25hbWUsXG4gICAgICAgIHNlcnZlck5hbWU6IHRvb2wuc2VydmVyX25hbWUsXG4gICAgICAgIHNlcnZlclR5cGU6IHRvb2wuc2VydmVyX3R5cGVcbiAgICAgIH07XG4gICAgfSk7XG4gIH0gZmluYWxseSB7XG4gICAgZGIuY2xvc2UoKTtcbiAgfVxufVxuXG4vKipcbiAqIOiOt+WPlk1DUOW3peWFt+WIl+ihqEFQSVxuICogR0VUIC9hcGkvbWNwL3Rvb2xzP3NlcnZlcj14eHgmcmVmcmVzaD10cnVlXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogUmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcbiAgICBjb25zdCBzZXJ2ZXJOYW1lID0gc2VhcmNoUGFyYW1zLmdldCgnc2VydmVyJyk7IC8vIOaMh+WumuacjeWKoeWZqOWQjeensFxuICAgIGNvbnN0IHJlZnJlc2ggPSBzZWFyY2hQYXJhbXMuZ2V0KCdyZWZyZXNoJykgPT09ICd0cnVlJzsgLy8g5piv5ZCm5by65Yi25Yi35pawXG4gICAgY29uc3QgaW5jbHVkZURpc2FibGVkID0gc2VhcmNoUGFyYW1zLmdldCgnaW5jbHVkZURpc2FibGVkJykgPT09ICd0cnVlJzsgLy8g5piv5ZCm5YyF5ZCr56aB55So55qE5bel5YW3XG4gICAgXG4gICAgbGV0IHRvb2xzOiBhbnlbXSA9IFtdO1xuICAgIFxuICAgIC8vIOS8mOWFiOS7juaVsOaNruW6k+iOt+WPluW3peWFt++8iOmZpOmdnuW8uuWItuWIt+aWsO+8iVxuICAgIGlmICghcmVmcmVzaCkge1xuICAgICAgdG9vbHMgPSBnZXRUb29sc0Zyb21EYXRhYmFzZShzZXJ2ZXJOYW1lID8/IHVuZGVmaW5lZCwgaW5jbHVkZURpc2FibGVkKTtcbiAgICAgIFxuICAgICAgLy8g55u05o6l6L+U5Zue5pWw5o2u5bqT5Lit55qE5bel5YW377yM5LiN5YaN6L+e5o6l5aSW6YOo5pyN5Yqh5ZmoXG4gICAgICBjb25zb2xlLmxvZyhg5LuO5pWw5o2u5bqT6I635Y+W5YiwICR7dG9vbHMubGVuZ3RofSDkuKrlt6XlhbdgKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIHRvb2xzOiB0b29scyxcbiAgICAgICAgZnJvbUNhY2hlOiB0cnVlXG4gICAgICB9KTtcbiAgICB9XG4gICAgXG4gICAgLy8g5aaC5p6c5pWw5o2u5bqT5Lit5rKh5pyJ5pWw5o2u5oiW5by65Yi25Yi35paw77yM5LuO5pyN5Yqh5Zmo6I635Y+WXG4gICAgY29uc29sZS5sb2coJ+S7juacjeWKoeWZqOiOt+WPluW3peWFt+WIl+ihqC4uLicpO1xuICAgIFxuICAgIC8vIOiOt+WPluacrOWcsE1DUOW3peWFt1xuICAgIGlmICghc2VydmVyTmFtZSB8fCBzZXJ2ZXJOYW1lID09PSAnbG9jYWwnKSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCB7IG1jcFNlcnZlckNsaWVudCB9ID0gcmVxdWlyZSgnLi4vLi4vLi4vLi4vbGliL21jcC9tY3AtY2xpZW50LXNlcnZlcicpO1xuICAgICAgICBpZiAobWNwU2VydmVyQ2xpZW50LmlzQ2xpZW50Q29ubmVjdGVkKCkpIHtcbiAgICAgICAgICBjb25zdCBsb2NhbFRvb2xzID0gbWNwU2VydmVyQ2xpZW50LmdldEF2YWlsYWJsZVRvb2xzKCk7XG4gICAgICAgICAgdG9vbHMucHVzaCguLi5sb2NhbFRvb2xzLm1hcCgodG9vbDogYW55KSA9PiAoe1xuICAgICAgICAgICAgLi4udG9vbCxcbiAgICAgICAgICAgIHNlcnZlck5hbWU6ICdsb2NhbCcsXG4gICAgICAgICAgICBzZXJ2ZXJUeXBlOiAnc3RkaW8nXG4gICAgICAgICAgfSkpKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5pys5ZywTUNQ5bel5YW35aSx6LSlOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gICAgXG4gICAgLy8g6I635Y+W5aSW6YOo5pyN5Yqh5ZmoTUNQ5bel5YW3XG4gICAgaWYgKCFzZXJ2ZXJOYW1lIHx8IHNlcnZlck5hbWUgIT09ICdsb2NhbCcpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIOS7juaVsOaNruW6k+iOt+WPluacjeWKoeWZqOmFjee9rlxuICAgICAgICBjb25zdCBjb25maWcgPSBnZXRTZXJ2ZXJzRnJvbURhdGFiYXNlKCk7XG4gICAgICAgIFxuICAgICAgICAvLyDlpoLmnpzmjIflrprkuobmnI3liqHlmajlkI3np7DvvIzlj6rojrflj5bor6XmnI3liqHlmajnmoTphY3nva5cbiAgICAgICAgY29uc3QgdGFyZ2V0Q29uZmlnOiBhbnkgPSB7fTtcbiAgICAgICAgaWYgKHNlcnZlck5hbWUgJiYgc2VydmVyTmFtZSAhPT0gJ2xvY2FsJykge1xuICAgICAgICAgIGlmIChjb25maWdbc2VydmVyTmFtZV0pIHtcbiAgICAgICAgICAgIHRhcmdldENvbmZpZ1tzZXJ2ZXJOYW1lXSA9IGNvbmZpZ1tzZXJ2ZXJOYW1lXTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAoIXNlcnZlck5hbWUpIHtcbiAgICAgICAgICBPYmplY3QuYXNzaWduKHRhcmdldENvbmZpZywgY29uZmlnKTtcbiAgICAgICAgfVxuICAgICAgICBcbiAgICAgICAgaWYgKE9iamVjdC5rZXlzKHRhcmdldENvbmZpZykubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdNQ1DmnI3liqHlmajphY3nva46JywgSlNPTi5zdHJpbmdpZnkodGFyZ2V0Q29uZmlnLCBudWxsLCAyKSk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8g6K6+572u6YWN572u5bm256Gu5L+d6L+e5o6lXG4gICAgICAgICAgbXVsdGlTZXJ2ZXJNY3BDbGllbnQuc2V0Q29uZmlnKHRhcmdldENvbmZpZyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+W8gOWni+i/nuaOpeaJgOaciU1DUOacjeWKoeWZqC4uLicpO1xuICAgICAgICAgIGF3YWl0IG11bHRpU2VydmVyTWNwQ2xpZW50LmNvbm5lY3RBbGwoKTtcbiAgICAgICAgICBcbiAgICAgICAgICAvLyDojrflj5bov57mjqXnirbmgIFcbiAgICAgICAgICBjb25zdCBjb25uZWN0aW9uU3RhdHVzID0gbXVsdGlTZXJ2ZXJNY3BDbGllbnQuZ2V0Q29ubmVjdGlvblN0YXR1cygpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdNQ1DmnI3liqHlmajov57mjqXnirbmgIE6JywgY29ubmVjdGlvblN0YXR1cyk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8g5Yi35paw5omA5pyJ5bel5YW3XG4gICAgICAgICAgY29uc29sZS5sb2coJ+W8gOWni+WIt+aWsOaJgOacieW3peWFty4uLicpO1xuICAgICAgICAgIGF3YWl0IG11bHRpU2VydmVyTWNwQ2xpZW50LnJlZnJlc2hBbGxUb29scygpO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIOiOt+WPluaJgOacieW3peWFt+WIl+ihqFxuICAgICAgICAgIGNvbnN0IG11bHRpU2VydmVyVG9vbHMgPSBtdWx0aVNlcnZlck1jcENsaWVudC5nZXRBbGxBdmFpbGFibGVUb29scygpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDojrflj5bliLAgJHttdWx0aVNlcnZlclRvb2xzLmxlbmd0aH0g5Liq5aSa5pyN5Yqh5ZmoTUNQ5bel5YW3OmAsIG11bHRpU2VydmVyVG9vbHMubWFwKHQgPT4gKHsgbmFtZTogdC5uYW1lLCBzZXJ2ZXI6IHQuc2VydmVyTmFtZSB9KSkpO1xuICAgICAgICAgIFxuICAgICAgICAgIC8vIOS/neWtmOW3peWFt+WIsOaVsOaNruW6k1xuICAgICAgICAgIGF3YWl0IHNhdmVUb29sc1RvRGF0YWJhc2UobXVsdGlTZXJ2ZXJUb29scyk7XG4gICAgICAgICAgXG4gICAgICAgICAgLy8g6L+H5ruk5oyH5a6a5pyN5Yqh5Zmo55qE5bel5YW3XG4gICAgICAgICAgaWYgKHNlcnZlck5hbWUpIHtcbiAgICAgICAgICAgIHRvb2xzLnB1c2goLi4ubXVsdGlTZXJ2ZXJUb29scy5maWx0ZXIodG9vbCA9PiB0b29sLnNlcnZlck5hbWUgPT09IHNlcnZlck5hbWUpKTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdG9vbHMucHVzaCguLi5tdWx0aVNlcnZlclRvb2xzKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWkmuacjeWKoeWZqE1DUOW3peWFt+Wksei0pTonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIGNvbnNvbGUubG9nKGDmgLvlhbHov5Tlm54gJHt0b29scy5sZW5ndGh9IOS4quW3peWFt2ApO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgdG9vbHM6IHRvb2xzLFxuICAgICAgZnJvbUNhY2hlOiBmYWxzZVxuICAgIH0pO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlk1DUOW3peWFt+WIl+ihqOWksei0pTonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ+iOt+WPluW3peWFt+WIl+ihqOWksei0pScgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn0iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwibXVsdGlTZXJ2ZXJNY3BDbGllbnQiLCJEYXRhYmFzZSIsInBhdGgiLCJkYlBhdGgiLCJqb2luIiwicHJvY2VzcyIsImN3ZCIsImdldFNlcnZlcnNGcm9tRGF0YWJhc2UiLCJkYiIsInNlcnZlcnMiLCJwcmVwYXJlIiwiYWxsIiwiY29uZmlnIiwiZm9yRWFjaCIsInNlcnZlciIsInNlcnZlckNvbmZpZyIsInR5cGUiLCJjb21tYW5kIiwiYXJncyIsIkpTT04iLCJwYXJzZSIsImVudiIsInVybCIsInRvb2xzIiwibmFtZSIsImNsb3NlIiwic2F2ZVRvb2xzVG9EYXRhYmFzZSIsInNlcnZlck1hcCIsIk1hcCIsInNldCIsImlkIiwidG9vbCIsInNlcnZlcklkIiwiZ2V0Iiwic2VydmVyTmFtZSIsImV4aXN0aW5nVG9vbCIsInJlc3VsdCIsInJ1biIsImRlc2NyaXB0aW9uIiwic3RyaW5naWZ5IiwiaW5wdXRTY2hlbWEiLCJjb25zb2xlIiwibG9nIiwibGVuZ3RoIiwiZXJyb3IiLCJnZXRUb29sc0Zyb21EYXRhYmFzZSIsImluY2x1ZGVEaXNhYmxlZCIsInF1ZXJ5IiwicGFyYW1zIiwicHVzaCIsIm1hcCIsImlucHV0X3NjaGVtYSIsIiRzY2hlbWEiLCJzZXJ2ZXJfaWQiLCJpc19hdmFpbGFibGUiLCJCb29sZWFuIiwiZW5hYmxlZCIsInVzYWdlX2NvdW50IiwiY3JlYXRlZF9hdCIsInVwZGF0ZWRfYXQiLCJzZXJ2ZXJfbmFtZSIsInNlcnZlclR5cGUiLCJzZXJ2ZXJfdHlwZSIsIkdFVCIsInJlcXVlc3QiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJyZWZyZXNoIiwidW5kZWZpbmVkIiwianNvbiIsInN1Y2Nlc3MiLCJmcm9tQ2FjaGUiLCJtY3BTZXJ2ZXJDbGllbnQiLCJyZXF1aXJlIiwiaXNDbGllbnRDb25uZWN0ZWQiLCJsb2NhbFRvb2xzIiwiZ2V0QXZhaWxhYmxlVG9vbHMiLCJ0YXJnZXRDb25maWciLCJPYmplY3QiLCJhc3NpZ24iLCJrZXlzIiwic2V0Q29uZmlnIiwiY29ubmVjdEFsbCIsImNvbm5lY3Rpb25TdGF0dXMiLCJnZXRDb25uZWN0aW9uU3RhdHVzIiwicmVmcmVzaEFsbFRvb2xzIiwibXVsdGlTZXJ2ZXJUb29scyIsImdldEFsbEF2YWlsYWJsZVRvb2xzIiwidCIsImZpbHRlciIsInN0YXR1cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mcp/tools/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client-server.ts":
/*!******************************************!*\
  !*** ./src/lib/mcp/mcp-client-server.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   McpServerClient: () => (/* binding */ McpServerClient),\n/* harmony export */   mcpServerClient: () => (/* binding */ mcpServerClient)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_stdio_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/stdio.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * 服务器端MCP客户端实现\n * 只能在Node.js环境中使用，用于API路由\n */ \n\n\n/**\n * 服务器端MCP客户端类\n * 负责与MCP服务器的通信\n */ class McpServerClient {\n    /**\n   * 连接到MCP服务器\n   */ async connect() {\n        try {\n            // 创建stdio传输，直接启动MCP服务器\n            const serverPath = path__WEBPACK_IMPORTED_MODULE_2__.join(process.cwd(), 'src', 'lib', 'mcp', 'mcp-server.ts');\n            this.transport = new _modelcontextprotocol_sdk_client_stdio_js__WEBPACK_IMPORTED_MODULE_1__.StdioClientTransport({\n                command: 'npx',\n                args: [\n                    'tsx',\n                    serverPath\n                ]\n            });\n            // 创建客户端\n            this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                name: 'kun-agent-server-client',\n                version: '1.0.0'\n            }, {\n                capabilities: {\n                    tools: {}\n                }\n            });\n            // 连接到服务器\n            await this.client.connect(this.transport);\n            this.isConnected = true;\n            // 获取可用工具\n            await this.refreshTools();\n            console.log('服务器端MCP客户端连接成功');\n            return true;\n        } catch (error) {\n            console.error('服务器端MCP客户端连接失败:', error);\n            await this.disconnect();\n            return false;\n        }\n    }\n    /**\n   * 断开连接\n   */ async disconnect() {\n        try {\n            if (this.client) {\n                await this.client.close();\n                this.client = null;\n            }\n            if (this.transport) {\n                await this.transport.close();\n                this.transport = null;\n            }\n            this.isConnected = false;\n            this.availableTools = [];\n            console.log('服务器端MCP客户端已断开连接');\n        } catch (error) {\n            console.error('断开MCP连接时出错:', error);\n        }\n    }\n    /**\n   * 检查是否已连接\n   */ isClientConnected() {\n        return this.isConnected && this.client !== null;\n    }\n    /**\n   * 刷新工具列表\n   */ async refreshTools() {\n        if (!this.client || !this.isConnected) {\n            return [];\n        }\n        try {\n            const response = await this.client.listTools();\n            this.availableTools = response.tools.map((tool)=>{\n                // 清理inputSchema，移除$schema字段以符合Ollama要求\n                const cleanInputSchema = tool.inputSchema ? {\n                    ...tool.inputSchema\n                } : {};\n                if (cleanInputSchema && typeof cleanInputSchema === 'object' && '$schema' in cleanInputSchema) {\n                    delete cleanInputSchema.$schema;\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description || '',\n                    inputSchema: cleanInputSchema\n                };\n            });\n            console.log(`已获取 ${this.availableTools.length} 个MCP工具`);\n            return this.availableTools;\n        } catch (error) {\n            console.error('获取MCP工具列表失败:', error);\n            return [];\n        }\n    }\n    /**\n   * 获取可用工具列表\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolCall) {\n        if (!this.client || !this.isConnected) {\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: 'MCP客户端未连接'\n                    }\n                ],\n                isError: true\n            };\n        }\n        try {\n            const result = await this.client.callTool({\n                name: toolCall.name,\n                arguments: toolCall.arguments\n            });\n            // 根据最新的MCP SDK，result.content 已经是正确的格式\n            const content = result.content || [\n                {\n                    type: 'text',\n                    text: '工具执行成功，但没有返回内容'\n                }\n            ];\n            return {\n                content: content.map((item)=>({\n                        type: 'text',\n                        text: item.type === 'text' ? item.text : JSON.stringify(item)\n                    })),\n                isError: false\n            };\n        } catch (error) {\n            console.error('工具调用失败:', error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : '未知错误'}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    constructor(){\n        this.client = null;\n        this.transport = null;\n        this.isConnected = false;\n        this.availableTools = [];\n    }\n}\n// 导出类和单例实例\n\nconst mcpServerClient = new McpServerClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client-server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client-sse.ts":
/*!***************************************!*\
  !*** ./src/lib/mcp/mcp-client-sse.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_sse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/sse.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/sse.js\");\n/**\n * SSE MCP客户端实现\n * 支持通过HTTP Server-Sent Events连接到远程MCP服务器\n */ \n\n/**\n * SSE MCP客户端类\n * 负责与远程SSE MCP服务器的通信\n */ class SSEMcpClient {\n    constructor(config){\n        this.client = null;\n        this.transport = null;\n        this.isConnected = false;\n        this.availableTools = [];\n        this.config = config;\n    }\n    /**\n   * 连接到SSE MCP服务器\n   */ async connect() {\n        const maxRetries = this.config.retryAttempts || 3;\n        const baseDelay = 1000; // 1秒基础延迟\n        // 根据MCP协议规范配置请求头（移到循环外部以便在catch块中访问）\n        const headers = {\n            'Accept': 'text/event-stream',\n            'Cache-Control': 'no-cache',\n            'Connection': 'keep-alive',\n            'Content-Type': 'application/json',\n            'User-Agent': 'kun-agent-sse-client/1.0.0'\n        };\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                console.log(`正在连接到SSE MCP服务器: ${this.config.url} (尝试 ${attempt}/${maxRetries})`);\n                // 创建SSE传输，添加必要的请求头\n                const url = new URL(this.config.url);\n                // 重置headers为基础配置\n                Object.keys(headers).forEach((key)=>{\n                    if (![\n                        'Accept',\n                        'Cache-Control',\n                        'Connection',\n                        'Content-Type',\n                        'User-Agent'\n                    ].includes(key)) {\n                        delete headers[key];\n                    }\n                });\n                // 重新设置基础请求头\n                headers['Accept'] = 'text/event-stream';\n                headers['Cache-Control'] = 'no-cache';\n                headers['Connection'] = 'keep-alive';\n                headers['Content-Type'] = 'application/json';\n                headers['User-Agent'] = 'kun-agent-sse-client/1.0.0';\n                // 添加MCP协议要求的会话ID头（生成唯一会话ID）\n                const sessionId = `mcp-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n                headers['Mcp-Session-Id'] = sessionId;\n                // 添加MCP协议版本头\n                const protocolVersion = this.config.protocolVersion || '2025-03-26';\n                headers['Mcp-Protocol-Version'] = protocolVersion;\n                // 添加CORS相关头部\n                headers['Access-Control-Request-Method'] = 'GET';\n                headers['Access-Control-Request-Headers'] = 'Content-Type, Authorization, Mcp-Session-Id, Mcp-Protocol-Version';\n                // 如果配置中包含API密钥，添加认证头\n                if (this.config.apiKey) {\n                    headers['Authorization'] = `Bearer ${this.config.apiKey}`;\n                }\n                // 合并自定义请求头\n                if (this.config.headers) {\n                    Object.assign(headers, this.config.headers);\n                }\n                // 创建传输配置\n                const transportConfig = {\n                    requestInit: {\n                        headers\n                    }\n                };\n                // 添加超时配置\n                if (this.config.timeout) {\n                    transportConfig.requestInit.timeout = this.config.timeout;\n                }\n                this.transport = new _modelcontextprotocol_sdk_client_sse_js__WEBPACK_IMPORTED_MODULE_1__.SSEClientTransport(url, transportConfig);\n                // 创建客户端\n                this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                    name: `kun-agent-sse-client-${this.config.name}`,\n                    version: '1.0.0'\n                }, {\n                    capabilities: {\n                        tools: {}\n                    }\n                });\n                // 连接到服务器\n                await this.client.connect(this.transport);\n                this.isConnected = true;\n                // 获取可用工具\n                await this.refreshTools();\n                console.log(`SSE MCP客户端连接成功: ${this.config.name}`);\n                return true;\n            } catch (error) {\n                console.error(`SSE MCP客户端连接失败 (${this.config.name}) - 尝试 ${attempt}:`, error);\n                // 检查错误类型\n                const is429Error = error?.code === 429 || error?.event?.code === 429 || error?.message?.includes('429') || error?.message?.includes('Too Many Requests');\n                const is412Error = error?.code === 412 || error?.event?.code === 412 || error?.message?.includes('412') || error?.message?.includes('Precondition Failed');\n                if (is412Error) {\n                    console.error(`检测到412错误 (${this.config.name})，这通常表示前置条件失败:`);\n                    console.error('可能的原因:');\n                    console.error('1. 服务器要求特定的认证头或API密钥');\n                    console.error('2. 请求头不符合MCP SSE协议要求');\n                    console.error('3. 服务器CORS配置不允许当前域名访问');\n                    console.error('4. 服务器不支持当前的MCP协议版本 (2025-03-26)');\n                    console.error('5. 会话ID格式不正确或服务器不接受会话管理');\n                    console.error('');\n                    console.error('解决建议:');\n                    console.error('- 检查服务器是否需要API密钥认证');\n                    console.error('- 验证服务器CORS配置是否正确');\n                    console.error('- 确认服务器支持MCP SSE传输协议');\n                    console.error('- 联系服务器管理员确认配置');\n                    // 使用Headers对象来处理请求头\n                    const headersObj = new Headers(headers);\n                    console.error(`当前请求头: ${JSON.stringify(Object.fromEntries(headersObj.entries()), null, 2)}`);\n                    // 对于412错误，不进行重试，因为这通常是配置问题\n                    return false;\n                }\n                if (is429Error) {\n                    console.warn(`检测到429错误 (${this.config.name})，这通常表示服务器过载或达到速率限制`);\n                    if (attempt < maxRetries) {\n                        // 指数退避延迟\n                        const delay = baseDelay * Math.pow(2, attempt - 1);\n                        console.log(`等待 ${delay}ms 后重试...`);\n                        await new Promise((resolve)=>setTimeout(resolve, delay));\n                        continue;\n                    }\n                }\n                await this.disconnect();\n                if (attempt === maxRetries) {\n                    if (is429Error) {\n                        console.error(`SSE MCP服务器 ${this.config.name} 持续返回429错误，可能是服务器过载或速率限制。请稍后再试。`);\n                    } else {\n                        console.error(`SSE MCP服务器 ${this.config.name} 连接失败，已尝试 ${maxRetries} 次`);\n                        console.error('可能的原因:');\n                        console.error('1. 服务器URL不正确或服务器未运行');\n                        console.error('2. 网络连接问题');\n                        console.error('3. 服务器不支持MCP SSE协议');\n                        console.error('4. 认证或权限问题');\n                        console.error('5. CORS配置问题');\n                        console.error(`错误详情: ${error?.message || '未知错误'}`);\n                    }\n                    return false;\n                }\n            }\n        }\n        return false;\n    }\n    /**\n   * 断开连接\n   */ async disconnect() {\n        try {\n            if (this.client) {\n                await this.client.close();\n                this.client = null;\n            }\n            if (this.transport) {\n                await this.transport.close();\n                this.transport = null;\n            }\n            this.isConnected = false;\n            this.availableTools = [];\n            console.log(`SSE MCP客户端已断开连接: ${this.config.name}`);\n        } catch (error) {\n            console.error(`断开SSE MCP连接时出错 (${this.config.name}):`, error);\n        }\n    }\n    /**\n   * 检查连接状态\n   */ getConnectionStatus() {\n        return this.isConnected;\n    }\n    /**\n   * 刷新可用工具列表\n   */ async refreshTools() {\n        if (!this.client || !this.isConnected) {\n            console.warn(`SSE MCP客户端未连接，无法刷新工具 (${this.config.name})`);\n            return;\n        }\n        try {\n            const response = await this.client.listTools();\n            this.availableTools = response.tools.map((tool)=>{\n                // 清理inputSchema，移除$schema字段以符合Ollama要求\n                const cleanInputSchema = tool.inputSchema ? {\n                    ...tool.inputSchema\n                } : {};\n                if (cleanInputSchema && typeof cleanInputSchema === 'object' && '$schema' in cleanInputSchema) {\n                    delete cleanInputSchema.$schema;\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description || '',\n                    inputSchema: cleanInputSchema\n                };\n            });\n            console.log(`已从SSE MCP服务器获取 ${this.availableTools.length} 个工具 (${this.config.name})`);\n        } catch (error) {\n            console.error(`刷新SSE MCP工具失败 (${this.config.name}):`, error);\n            this.availableTools = [];\n        }\n    }\n    /**\n   * 获取可用工具列表\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolName, args) {\n        if (!this.client || !this.isConnected) {\n            throw new Error(`SSE MCP客户端未连接 (${this.config.name})`);\n        }\n        try {\n            console.log(`调用SSE MCP工具: ${toolName} (${this.config.name})`, args);\n            const result = await this.client.callTool({\n                name: toolName,\n                arguments: args\n            });\n            // 确保返回的结果格式正确\n            if (result.content && Array.isArray(result.content)) {\n                return {\n                    content: result.content.map((item)=>({\n                            type: 'text',\n                            text: typeof item === 'string' ? item : typeof item === 'object' && item.text ? item.text : JSON.stringify(item)\n                        }))\n                };\n            } else {\n                return {\n                    content: [\n                        {\n                            type: 'text',\n                            text: typeof result === 'string' ? result : JSON.stringify(result)\n                        }\n                    ]\n                };\n            }\n        } catch (error) {\n            console.error(`SSE MCP工具调用失败 (${this.config.name}):`, error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : String(error)}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    /**\n   * 获取服务器配置\n   */ getConfig() {\n        return this.config;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SSEMcpClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client-sse.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client-streamable-http.ts":
/*!***************************************************!*\
  !*** ./src/lib/mcp/mcp-client-streamable-http.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_streamableHttp_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/streamableHttp.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js\");\n/**\n * Streamable HTTP MCP客户端实现\n * 支持通过Streamable HTTP连接到远程MCP服务器\n * 这是官方推荐的传输方式，替代已弃用的SSE传输\n */ \n\n/**\n * Streamable HTTP MCP客户端类\n * 负责与远程Streamable HTTP MCP服务器的通信\n */ class StreamableHTTPMcpClient {\n    constructor(config){\n        this.client = null;\n        this.transport = null;\n        this.isConnected = false;\n        this.availableTools = [];\n        this.config = config;\n    }\n    /**\n   * 检测VPN环境\n   */ async detectVPNEnvironment() {\n        try {\n            // 检查常见的VPN环境指标\n            const userAgent = navigator.userAgent;\n            const hasVPNIndicators = /VPN|Proxy|Tunnel/i.test(userAgent);\n            // 检查网络延迟（简单的VPN检测）\n            const startTime = Date.now();\n            await fetch('data:text/plain,', {\n                method: 'HEAD'\n            });\n            const latency = Date.now() - startTime;\n            // 如果延迟超过100ms，可能在使用VPN\n            return hasVPNIndicators || latency > 100;\n        } catch  {\n            return false;\n        }\n    }\n    /**\n   * 使用VPN兼容模式连接\n   */ async connectWithVPNMode() {\n        const originalVPNSetting = this.config.vpnCompatible;\n        try {\n            // 临时启用VPN兼容模式\n            this.config.vpnCompatible = true;\n            const result = await this.connect();\n            return result;\n        } finally{\n            // 恢复原始设置\n            this.config.vpnCompatible = originalVPNSetting;\n        }\n    }\n    /**\n   * 连接到Streamable HTTP MCP服务器\n   */ async connect() {\n        const maxRetries = this.config.retryAttempts || 3;\n        const baseDelay = 1000; // 1秒基础延迟\n        // 检测VPN环境\n        const isVPNDetected = await this.detectVPNEnvironment();\n        const isVPNMode = this.config.vpnCompatible || isVPNDetected;\n        // 根据MCP Streamable HTTP协议规范配置请求头\n        const headers = {\n            'Accept': 'application/json, text/event-stream',\n            'Content-Type': 'application/json',\n            'User-Agent': 'kun-agent-streamable-http-client/1.0.0'\n        };\n        // VPN兼容模式下的特殊配置\n        if (isVPNMode) {\n            headers['X-Forwarded-For'] = '127.0.0.1';\n            headers['X-Real-IP'] = '127.0.0.1';\n            headers['Pragma'] = 'no-cache';\n            headers['Expires'] = '0';\n        } else {\n            // 非VPN模式下添加CORS相关头部\n            headers['Cache-Control'] = 'no-cache';\n            headers['Connection'] = 'keep-alive';\n        }\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                console.log(`正在连接到Streamable HTTP MCP服务器: ${this.config.url} (尝试 ${attempt}/${maxRetries})`);\n                // 创建URL对象\n                const url = new URL(this.config.url);\n                // 重置headers为基础配置\n                Object.keys(headers).forEach((key)=>{\n                    if (![\n                        'Accept',\n                        'Content-Type',\n                        'User-Agent'\n                    ].includes(key)) {\n                        delete headers[key];\n                    }\n                });\n                // 重新设置基础请求头\n                headers['Accept'] = 'application/json, text/event-stream';\n                headers['Content-Type'] = 'application/json';\n                headers['User-Agent'] = 'kun-agent-streamable-http-client/1.0.0';\n                // VPN兼容模式配置\n                if (isVPNMode) {\n                    headers['X-Forwarded-For'] = '127.0.0.1';\n                    headers['X-Real-IP'] = '127.0.0.1';\n                    headers['Pragma'] = 'no-cache';\n                    headers['Expires'] = '0';\n                } else {\n                    headers['Cache-Control'] = 'no-cache';\n                    headers['Connection'] = 'keep-alive';\n                }\n                // 添加MCP协议要求的会话ID头（生成唯一会话ID）\n                const sessionId = `mcp-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n                headers['Mcp-Session-Id'] = sessionId;\n                // 添加MCP协议版本头\n                const protocolVersion = this.config.protocolVersion || '2025-03-26';\n                headers['Mcp-Protocol-Version'] = protocolVersion;\n                // 如果配置中包含API密钥，添加认证头\n                if (this.config.apiKey) {\n                    headers['Authorization'] = `Bearer ${this.config.apiKey}`;\n                }\n                // 合并自定义请求头\n                if (this.config.headers) {\n                    Object.assign(headers, this.config.headers);\n                }\n                // 创建传输配置\n                const transportConfig = {\n                    headers\n                };\n                // VPN兼容模式下的传输配置\n                if (isVPNMode) {\n                    // 延长超时时间\n                    transportConfig.timeout = (this.config.timeout || 15000) * 2;\n                    transportConfig.keepalive = false;\n                    transportConfig.cache = 'no-store';\n                    transportConfig.redirect = 'follow';\n                } else {\n                    // 添加超时配置\n                    if (this.config.timeout) {\n                        transportConfig.timeout = this.config.timeout;\n                    }\n                }\n                // 创建Streamable HTTP传输\n                this.transport = new _modelcontextprotocol_sdk_client_streamableHttp_js__WEBPACK_IMPORTED_MODULE_1__.StreamableHTTPClientTransport(url, transportConfig);\n                // 创建客户端\n                this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                    name: `kun-agent-streamable-http-client-${this.config.name}`,\n                    version: '1.0.0'\n                }, {\n                    capabilities: {\n                        tools: {}\n                    }\n                });\n                // 连接到服务器\n                await this.client.connect(this.transport);\n                this.isConnected = true;\n                // 获取可用工具\n                await this.refreshTools();\n                console.log(`Streamable HTTP MCP客户端连接成功: ${this.config.name}`);\n                return true;\n            } catch (error) {\n                console.error(`Streamable HTTP MCP客户端连接失败 (${this.config.name}) - 尝试 ${attempt}:`, error);\n                // 检查错误类型\n                const is429Error = error?.code === 429 || error?.status === 429 || error?.message?.includes('429') || error?.message?.includes('Too Many Requests');\n                const is412Error = error?.code === 412 || error?.status === 412 || error?.message?.includes('412') || error?.message?.includes('Precondition Failed');\n                const is422Error = error?.code === 422 || error?.status === 422 || error?.message?.includes('422') || error?.message?.includes('Unprocessable Entity');\n                // 检测VPN相关错误\n                const isVPNRelatedError = (is412Error || is422Error) && (error?.message?.toLowerCase().includes('proxy') || error?.message?.toLowerCase().includes('vpn') || error?.message?.toLowerCase().includes('tunnel') || error?.message?.toLowerCase().includes('network') || error?.message?.toLowerCase().includes('timeout'));\n                if (is412Error || is422Error) {\n                    const errorType = is412Error ? '412 (Precondition Failed)' : '422 (Unprocessable Entity)';\n                    console.error(`检测到${errorType}错误 (${this.config.name})，这通常表示:`);\n                    if (isVPNMode) {\n                        console.error('VPN模式下的可能原因:');\n                        console.error('1. VPN服务器与目标服务器之间的网络问题');\n                        console.error('2. VPN节点被目标服务器屏蔽');\n                        console.error('3. VPN协议与服务器不兼容');\n                        console.error('解决建议: 尝试切换VPN节点或关闭VPN');\n                    } else {\n                        console.error('可能的原因:');\n                        console.error('1. 服务器要求特定的认证头或API密钥');\n                        console.error('2. 请求头不符合MCP Streamable HTTP协议要求');\n                        console.error('3. 服务器CORS配置不允许当前域名访问');\n                        console.error('4. 服务器不支持当前的MCP协议版本');\n                        console.error('5. 会话ID格式不正确或服务器不接受会话管理');\n                        console.error('解决建议:');\n                        console.error('- 检查服务器是否需要API密钥认证');\n                        console.error('- 验证服务器CORS配置是否正确');\n                        console.error('- 确认服务器支持MCP Streamable HTTP传输协议');\n                    }\n                    console.error(`当前请求头: ${JSON.stringify(headers, null, 2)}`);\n                    // 对于412和422错误，不进行重试，因为这通常是配置问题\n                    return false;\n                }\n                // 如果检测到VPN相关错误且当前不在VPN模式，尝试VPN兼容模式\n                if (isVPNRelatedError && !isVPNMode && attempt < maxRetries) {\n                    console.log(`检测到VPN相关错误，尝试使用VPN兼容模式重新连接...`);\n                    return await this.connectWithVPNMode();\n                }\n                if (is429Error) {\n                    console.warn(`检测到429错误 (${this.config.name})，这通常表示服务器过载或达到速率限制`);\n                    if (attempt < maxRetries) {\n                        // 指数退避延迟\n                        const delay = baseDelay * Math.pow(2, attempt - 1);\n                        console.log(`等待 ${delay}ms 后重试...`);\n                        await new Promise((resolve)=>setTimeout(resolve, delay));\n                        continue;\n                    }\n                }\n                await this.disconnect();\n                if (attempt === maxRetries) {\n                    if (is429Error) {\n                        console.error(`Streamable HTTP MCP服务器 ${this.config.name} 持续返回429错误，可能是服务器过载或速率限制。请稍后再试。`);\n                    } else {\n                        console.error(`Streamable HTTP MCP服务器 ${this.config.name} 连接失败，已尝试 ${maxRetries} 次`);\n                        console.error('可能的原因:');\n                        console.error('1. 服务器URL不正确或服务器未运行');\n                        console.error('2. 网络连接问题');\n                        console.error('3. 服务器不支持MCP Streamable HTTP协议');\n                        console.error('4. 认证或权限问题');\n                        console.error('5. CORS配置问题');\n                        console.error(`错误详情: ${error?.message || '未知错误'}`);\n                    }\n                    return false;\n                }\n            }\n        }\n        return false;\n    }\n    /**\n   * 断开连接\n   */ async disconnect() {\n        try {\n            if (this.client) {\n                await this.client.close();\n                this.client = null;\n            }\n            if (this.transport) {\n                await this.transport.close();\n                this.transport = null;\n            }\n            this.isConnected = false;\n            this.availableTools = [];\n            console.log(`Streamable HTTP MCP客户端已断开连接: ${this.config.name}`);\n        } catch (error) {\n            console.error(`断开Streamable HTTP MCP连接时出错 (${this.config.name}):`, error);\n        }\n    }\n    /**\n   * 检查连接状态\n   */ getConnectionStatus() {\n        return this.isConnected;\n    }\n    /**\n   * 刷新可用工具列表\n   */ async refreshTools() {\n        if (!this.client || !this.isConnected) {\n            console.warn(`Streamable HTTP MCP客户端未连接，无法刷新工具 (${this.config.name})`);\n            return;\n        }\n        try {\n            const response = await this.client.listTools();\n            this.availableTools = response.tools.map((tool)=>{\n                // 清理inputSchema，移除$schema字段以符合Ollama要求\n                const cleanInputSchema = tool.inputSchema ? {\n                    ...tool.inputSchema\n                } : {};\n                if (cleanInputSchema && typeof cleanInputSchema === 'object' && '$schema' in cleanInputSchema) {\n                    delete cleanInputSchema.$schema;\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description || '',\n                    inputSchema: cleanInputSchema\n                };\n            });\n            console.log(`已从Streamable HTTP MCP服务器获取 ${this.availableTools.length} 个工具 (${this.config.name})`);\n        } catch (error) {\n            console.error(`刷新Streamable HTTP MCP工具失败 (${this.config.name}):`, error);\n            this.availableTools = [];\n        }\n    }\n    /**\n   * 获取可用工具列表\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolName, args) {\n        if (!this.client || !this.isConnected) {\n            throw new Error(`Streamable HTTP MCP客户端未连接 (${this.config.name})`);\n        }\n        try {\n            console.log(`调用Streamable HTTP MCP工具: ${toolName} (${this.config.name})`, args);\n            const result = await this.client.callTool({\n                name: toolName,\n                arguments: args\n            });\n            // 确保返回的结果格式正确\n            if (result.content && Array.isArray(result.content)) {\n                return {\n                    content: result.content.map((item)=>({\n                            type: 'text',\n                            text: typeof item === 'string' ? item : typeof item === 'object' && item.text ? item.text : JSON.stringify(item)\n                        }))\n                };\n            } else {\n                return {\n                    content: [\n                        {\n                            type: 'text',\n                            text: typeof result === 'string' ? result : JSON.stringify(result)\n                        }\n                    ]\n                };\n            }\n        } catch (error) {\n            console.error(`Streamable HTTP MCP工具调用失败 (${this.config.name}):`, error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : String(error)}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    /**\n   * 获取服务器配置\n   */ getConfig() {\n        return this.config;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StreamableHTTPMcpClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client-streamable-http.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-multi-server-client.ts":
/*!************************************************!*\
  !*** ./src/lib/mcp/mcp-multi-server-client.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   multiServerMcpClient: () => (/* binding */ multiServerMcpClient)\n/* harmony export */ });\n/* harmony import */ var _mcp_client_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mcp-client-server */ \"(rsc)/./src/lib/mcp/mcp-client-server.ts\");\n/* harmony import */ var _mcp_client_sse__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mcp-client-sse */ \"(rsc)/./src/lib/mcp/mcp-client-sse.ts\");\n/* harmony import */ var _mcp_client_streamable_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mcp-client-streamable-http */ \"(rsc)/./src/lib/mcp/mcp-client-streamable-http.ts\");\n/**\n * 多服务器MCP客户端管理器\n * 支持同时管理本地stdio、远程SSE和Streamable HTTP MCP服务器\n */ \n\n\n/**\n * 多服务器MCP客户端管理器\n * 统一管理多个MCP服务器连接\n */ class MultiServerMcpClient {\n    constructor(){\n        this.stdioClient = null;\n        this.sseClients = new Map();\n        this.streamableHTTPClients = new Map();\n        this.config = {};\n        // 初始化本地stdio客户端\n        this.stdioClient = new _mcp_client_server__WEBPACK_IMPORTED_MODULE_0__.McpServerClient();\n    }\n    /**\n   * 设置服务器配置\n   */ setConfig(config) {\n        this.config = config;\n    }\n    /**\n   * 连接到所有配置的服务器\n   */ async connectAll() {\n        const results = {};\n        // 连接本地stdio服务器（如果存在）\n        if (this.stdioClient) {\n            try {\n                const connected = await this.stdioClient.connect();\n                results['local'] = connected;\n            } catch (error) {\n                console.error('本地stdio MCP服务器连接失败:', error);\n                results['local'] = false;\n            }\n        }\n        // 连接所有配置的远程服务器\n        console.log('开始连接远程MCP服务器，配置数量:', Object.keys(this.config).length);\n        console.log('配置详情:', this.config);\n        for (const [serverName, serverConfig] of Object.entries(this.config)){\n            console.log(`处理服务器: ${serverName}, 配置:`, serverConfig);\n            // 检查服务器是否被禁用\n            if (serverConfig.enabled === false) {\n                console.log(`MCP服务器 ${serverName} 已被禁用，跳过连接`);\n                results[serverName] = false;\n                continue;\n            }\n            if (serverConfig.type === 'sse' && serverConfig.url) {\n                try {\n                    const sseConfig = {\n                        name: serverName,\n                        url: serverConfig.url,\n                        type: 'sse',\n                        apiKey: serverConfig.apiKey,\n                        headers: serverConfig.headers,\n                        timeout: serverConfig.timeout,\n                        retryAttempts: serverConfig.retryAttempts,\n                        protocolVersion: serverConfig.protocolVersion\n                    };\n                    console.log(`尝试连接SSE服务器: ${serverName}, URL: ${serverConfig.url}`);\n                    const sseClient = new _mcp_client_sse__WEBPACK_IMPORTED_MODULE_1__[\"default\"](sseConfig);\n                    const connected = await sseClient.connect();\n                    if (connected) {\n                        this.sseClients.set(serverName, sseClient);\n                        console.log(`SSE MCP服务器 ${serverName} 连接成功`);\n                        // 获取并显示工具\n                        const tools = sseClient.getAvailableTools();\n                        console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                    } else {\n                        console.warn(`SSE MCP服务器 ${serverName} 连接失败，将跳过此服务器`);\n                    }\n                    results[serverName] = connected;\n                } catch (error) {\n                    console.error(`SSE MCP服务器连接失败 (${serverName}):`, error);\n                    results[serverName] = false;\n                }\n            } else if (serverConfig.type === 'streamable-http' && serverConfig.url) {\n                try {\n                    const streamableHTTPConfig = {\n                        name: serverName,\n                        url: serverConfig.url,\n                        type: 'streamable-http',\n                        apiKey: serverConfig.apiKey,\n                        headers: serverConfig.headers,\n                        timeout: serverConfig.timeout,\n                        retryAttempts: serverConfig.retryAttempts,\n                        protocolVersion: serverConfig.protocolVersion,\n                        vpnCompatible: serverConfig.vpnCompatible\n                    };\n                    console.log(`尝试连接Streamable HTTP服务器: ${serverName}, URL: ${serverConfig.url}`);\n                    const streamableHTTPClient = new _mcp_client_streamable_http__WEBPACK_IMPORTED_MODULE_2__[\"default\"](streamableHTTPConfig);\n                    const connected = await streamableHTTPClient.connect();\n                    if (connected) {\n                        this.streamableHTTPClients.set(serverName, streamableHTTPClient);\n                        console.log(`Streamable HTTP MCP服务器 ${serverName} 连接成功`);\n                        // 获取并显示工具\n                        const tools = streamableHTTPClient.getAvailableTools();\n                        console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                    } else {\n                        console.warn(`Streamable HTTP MCP服务器 ${serverName} 连接失败，将跳过此服务器`);\n                    }\n                    results[serverName] = connected;\n                } catch (error) {\n                    console.error(`Streamable HTTP MCP服务器连接失败 (${serverName}):`, error);\n                    results[serverName] = false;\n                }\n            } else {\n                console.log(`跳过服务器 ${serverName}: type=${serverConfig.type}, url=${serverConfig.url}`);\n            }\n        }\n        return results;\n    }\n    /**\n   * 连接单个服务器\n   */ async connectServer(serverName) {\n        const serverConfig = this.config[serverName];\n        if (!serverConfig) {\n            console.error(`服务器配置不存在: ${serverName}`);\n            return false;\n        }\n        console.log(`连接单个服务器: ${serverName}, 配置:`, serverConfig);\n        // 检查服务器是否被禁用\n        if (serverConfig.enabled === false) {\n            console.log(`MCP服务器 ${serverName} 已被禁用，跳过连接`);\n            return false;\n        }\n        try {\n            if (serverConfig.type === 'sse' && serverConfig.url) {\n                const sseConfig = {\n                    name: serverName,\n                    url: serverConfig.url,\n                    type: 'sse',\n                    apiKey: serverConfig.apiKey,\n                    headers: serverConfig.headers,\n                    timeout: serverConfig.timeout,\n                    retryAttempts: serverConfig.retryAttempts,\n                    protocolVersion: serverConfig.protocolVersion\n                };\n                console.log(`尝试连接SSE服务器: ${serverName}, URL: ${serverConfig.url}`);\n                const sseClient = new _mcp_client_sse__WEBPACK_IMPORTED_MODULE_1__[\"default\"](sseConfig);\n                const connected = await sseClient.connect();\n                if (connected) {\n                    this.sseClients.set(serverName, sseClient);\n                    console.log(`SSE MCP服务器 ${serverName} 连接成功`);\n                    // 获取并显示工具\n                    const tools = sseClient.getAvailableTools();\n                    console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                } else {\n                    console.warn(`SSE MCP服务器 ${serverName} 连接失败`);\n                }\n                return connected;\n            } else if (serverConfig.type === 'streamable-http' && serverConfig.url) {\n                const streamableHTTPConfig = {\n                    name: serverName,\n                    url: serverConfig.url,\n                    type: 'streamable-http',\n                    apiKey: serverConfig.apiKey,\n                    headers: serverConfig.headers,\n                    timeout: serverConfig.timeout,\n                    retryAttempts: serverConfig.retryAttempts,\n                    protocolVersion: serverConfig.protocolVersion,\n                    vpnCompatible: serverConfig.vpnCompatible\n                };\n                console.log(`尝试连接Streamable HTTP服务器: ${serverName}, URL: ${serverConfig.url}`);\n                const streamableHTTPClient = new _mcp_client_streamable_http__WEBPACK_IMPORTED_MODULE_2__[\"default\"](streamableHTTPConfig);\n                const connected = await streamableHTTPClient.connect();\n                if (connected) {\n                    this.streamableHTTPClients.set(serverName, streamableHTTPClient);\n                    console.log(`Streamable HTTP MCP服务器 ${serverName} 连接成功`);\n                    // 获取并显示工具\n                    const tools = streamableHTTPClient.getAvailableTools();\n                    console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                } else {\n                    console.warn(`Streamable HTTP MCP服务器 ${serverName} 连接失败`);\n                }\n                return connected;\n            } else {\n                console.log(`跳过服务器 ${serverName}: type=${serverConfig.type}, url=${serverConfig.url}`);\n                return false;\n            }\n        } catch (error) {\n            console.error(`连接MCP服务器失败 (${serverName}):`, error);\n            return false;\n        }\n    }\n    /**\n   * 智能连接：只连接包含指定工具的服务器\n   */ async connectForTool(toolName) {\n        // 首先检查本地工具\n        if (this.stdioClient && this.stdioClient.isToolAvailable(toolName)) {\n            if (!this.stdioClient.isClientConnected()) {\n                const connected = await this.stdioClient.connect();\n                if (connected) {\n                    console.log(`为工具 ${toolName} 连接了本地服务器`);\n                    return 'local';\n                }\n            } else {\n                return 'local';\n            }\n        }\n        // 检查已连接的远程服务器\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            if (sseClient.getConnectionStatus() && sseClient.isToolAvailable(toolName)) {\n                console.log(`工具 ${toolName} 在已连接的SSE服务器 ${serverName} 中找到`);\n                return serverName;\n            }\n        }\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            if (streamableHTTPClient.getConnectionStatus() && streamableHTTPClient.isToolAvailable(toolName)) {\n                console.log(`工具 ${toolName} 在已连接的Streamable HTTP服务器 ${serverName} 中找到`);\n                return serverName;\n            }\n        }\n        // 如果没有找到，尝试连接可能包含该工具的服务器\n        // 这里我们需要连接所有未连接的服务器来查找工具\n        for (const [serverName, serverConfig] of Object.entries(this.config)){\n            // 检查服务器是否被禁用\n            if (serverConfig.enabled === false) {\n                continue;\n            }\n            // 检查服务器是否已连接\n            const isSSEConnected = this.sseClients.has(serverName) && this.sseClients.get(serverName)?.getConnectionStatus();\n            const isStreamableHTTPConnected = this.streamableHTTPClients.has(serverName) && this.streamableHTTPClients.get(serverName)?.getConnectionStatus();\n            if (!isSSEConnected && !isStreamableHTTPConnected) {\n                console.log(`尝试连接服务器 ${serverName} 来查找工具 ${toolName}`);\n                const connected = await this.connectServer(serverName);\n                if (connected) {\n                    // 检查新连接的服务器是否有该工具\n                    if (this.isToolAvailable(toolName, serverName)) {\n                        console.log(`在新连接的服务器 ${serverName} 中找到工具 ${toolName}`);\n                        return serverName;\n                    }\n                }\n            }\n        }\n        console.log(`未找到工具 ${toolName} 对应的服务器`);\n        return null;\n    }\n    /**\n   * 断开指定服务器的连接\n   */ async disconnectServer(serverName) {\n        if (serverName === 'local') {\n            // 本地服务器不支持断开连接\n            console.log('本地服务器不支持断开连接操作');\n            return;\n        }\n        // 断开SSE服务器连接\n        const sseClient = this.sseClients.get(serverName);\n        if (sseClient) {\n            await sseClient.disconnect();\n            this.sseClients.delete(serverName);\n            console.log(`已断开SSE服务器 ${serverName} 的连接`);\n            return;\n        }\n        // 断开Streamable HTTP服务器连接\n        const streamableHTTPClient = this.streamableHTTPClients.get(serverName);\n        if (streamableHTTPClient) {\n            await streamableHTTPClient.disconnect();\n            this.streamableHTTPClients.delete(serverName);\n            console.log(`已断开Streamable HTTP服务器 ${serverName} 的连接`);\n            return;\n        }\n        console.log(`服务器 ${serverName} 不存在或未连接`);\n    }\n    /**\n   * 断开所有连接\n   */ async disconnectAll() {\n        // 断开本地stdio连接\n        if (this.stdioClient) {\n            await this.stdioClient.disconnect();\n        }\n        // 断开所有SSE连接\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            await sseClient.disconnect();\n        }\n        this.sseClients.clear();\n        // 断开所有Streamable HTTP连接\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            await streamableHTTPClient.disconnect();\n        }\n        this.streamableHTTPClients.clear();\n    }\n    /**\n   * 获取所有服务器的连接状态\n   */ getConnectionStatus() {\n        const status = {};\n        // 本地stdio服务器状态\n        if (this.stdioClient) {\n            status['local'] = this.stdioClient.isClientConnected();\n        }\n        // SSE服务器状态\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            status[serverName] = sseClient.getConnectionStatus();\n        }\n        // Streamable HTTP服务器状态\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            status[serverName] = streamableHTTPClient.getConnectionStatus();\n        }\n        return status;\n    }\n    /**\n   * 刷新所有服务器的工具列表\n   */ async refreshAllTools() {\n        // 刷新本地stdio工具\n        if (this.stdioClient && this.stdioClient.isClientConnected()) {\n            await this.stdioClient.refreshTools();\n        }\n        // 刷新所有SSE服务器工具\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            if (sseClient.getConnectionStatus()) {\n                await sseClient.refreshTools();\n            }\n        }\n        // 刷新所有Streamable HTTP服务器工具\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            if (streamableHTTPClient.getConnectionStatus()) {\n                await streamableHTTPClient.refreshTools();\n            }\n        }\n    }\n    /**\n   * 获取所有可用工具（包含服务器信息）\n   */ getAllAvailableTools() {\n        const allTools = [];\n        // 添加本地stdio工具\n        if (this.stdioClient && this.stdioClient.isClientConnected()) {\n            const localTools = this.stdioClient.getAvailableTools();\n            allTools.push(...localTools.map((tool)=>({\n                    ...tool,\n                    serverName: 'local',\n                    serverType: 'stdio'\n                })));\n        }\n        // 添加所有SSE服务器工具\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            if (sseClient.getConnectionStatus()) {\n                const sseTools = sseClient.getAvailableTools();\n                allTools.push(...sseTools.map((tool)=>({\n                        ...tool,\n                        serverName,\n                        serverType: 'sse'\n                    })));\n            }\n        }\n        // 添加所有Streamable HTTP服务器工具\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            if (streamableHTTPClient.getConnectionStatus()) {\n                const streamableHTTPTools = streamableHTTPClient.getAvailableTools();\n                allTools.push(...streamableHTTPTools.map((tool)=>({\n                        ...tool,\n                        serverName,\n                        serverType: 'streamable-http'\n                    })));\n            }\n        }\n        return allTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName, serverName) {\n        if (serverName) {\n            // 检查特定服务器\n            if (serverName === 'local' && this.stdioClient) {\n                return this.stdioClient.isToolAvailable(toolName);\n            }\n            const sseClient = this.sseClients.get(serverName);\n            if (sseClient) {\n                return sseClient.isToolAvailable(toolName);\n            }\n            const streamableHTTPClient = this.streamableHTTPClients.get(serverName);\n            if (streamableHTTPClient) {\n                return streamableHTTPClient.isToolAvailable(toolName);\n            }\n            return false;\n        } else {\n            // 检查所有服务器\n            return this.getAllAvailableTools().some((tool)=>tool.name === toolName);\n        }\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolName, args, serverName) {\n        // 如果指定了服务器名称\n        if (serverName) {\n            if (serverName === 'local' && this.stdioClient) {\n                return await this.stdioClient.callTool({\n                    name: toolName,\n                    arguments: args\n                });\n            }\n            const sseClient = this.sseClients.get(serverName);\n            if (sseClient) {\n                return await sseClient.callTool(toolName, args);\n            }\n            const streamableHTTPClient = this.streamableHTTPClients.get(serverName);\n            if (streamableHTTPClient) {\n                return await streamableHTTPClient.callTool(toolName, args);\n            }\n            throw new Error(`服务器 '${serverName}' 不存在或未连接`);\n        }\n        // 自动查找工具所在的服务器\n        const allTools = this.getAllAvailableTools();\n        const tool = allTools.find((t)=>t.name === toolName);\n        if (!tool) {\n            throw new Error(`工具 '${toolName}' 不存在`);\n        }\n        // 递归调用，指定服务器名称\n        return await this.callTool(toolName, args, tool.serverName);\n    }\n    /**\n   * 获取特定服务器的工具列表\n   */ getToolsByServer(serverName) {\n        if (serverName === 'local' && this.stdioClient) {\n            return this.stdioClient.getAvailableTools();\n        }\n        const sseClient = this.sseClients.get(serverName);\n        if (sseClient) {\n            return sseClient.getAvailableTools();\n        }\n        return [];\n    }\n    /**\n   * 获取服务器列表\n   */ getServerList() {\n        const servers = [\n            'local'\n        ];\n        servers.push(...Array.from(this.sseClients.keys()));\n        return servers;\n    }\n}\n// 导出单例实例\nconst multiServerMcpClient = new MultiServerMcpClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultiServerMcpClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-multi-server-client.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ajv","vendor-chunks/zod","vendor-chunks/@modelcontextprotocol","vendor-chunks/uri-js","vendor-chunks/cross-spawn","vendor-chunks/which","vendor-chunks/isexe","vendor-chunks/json-schema-traverse","vendor-chunks/fast-json-stable-stringify","vendor-chunks/fast-deep-equal","vendor-chunks/path-key","vendor-chunks/shebang-command","vendor-chunks/shebang-regex","vendor-chunks/eventsource","vendor-chunks/eventsource-parser","vendor-chunks/pkce-challenge"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();