'use client';

import React from 'react';
import { Conversation } from '../../../lib/database';

interface ChatHeaderProps {
  currentConversation: Conversation | null;
}

export function ChatHeader({ currentConversation }: ChatHeaderProps) {
  if (!currentConversation) {
    return (
      <div className="p-4 border-b border-theme-border bg-theme-card transition-colors duration-300">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-theme-foreground">
              新对话
            </h2>
            <p className="text-sm text-theme-foreground-muted">
              选择模型开始对话
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border-b border-theme-border bg-theme-card transition-colors duration-300">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-theme-foreground">
            {currentConversation.title}
          </h2>
          <p className="text-sm text-theme-foreground-muted">
            模型: {currentConversation.model}
          </p>
        </div>
      </div>
    </div>
  );
}