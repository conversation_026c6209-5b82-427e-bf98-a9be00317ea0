'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON>g, <PERSON>, Loader2 } from 'lucide-react';
import { AIState } from '../types';

interface AIStatusIndicatorProps {
  aiState: AIState;
}

export function AIStatusIndicator({ aiState }: AIStatusIndicatorProps) {
  if (aiState.status === 'idle') {
    return null;
  }

  const getStatusConfig = () => {
    switch (aiState.status) {
      case 'loading':
         return {
           icon: Loader2,
           text: aiState.message || '正在加载模型...',
           color: 'text-orange-600',
           bgColor: 'bg-orange-50',
           borderColor: 'border-orange-200'
         };
      case 'generating':
        return {
          icon: Bot,
          text: '正在生成回复...',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case 'tool_calling':
        return {
          icon: Cog,
          text: aiState.toolName ? `正在调用 ${aiState.toolName}...` : '正在调用工具...',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'thinking':
        return {
          icon: Brain,
          text: '正在思考中...',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200'
        };
      default:
        return null;
    }
  };

  const config = getStatusConfig();

  if (!config) {
    return null;
  }

  const Icon = config.icon;

  return (
    <div className="flex items-center">
      {/* 为loading状态显示3D立方体加载动画 */}
      {aiState.status === 'loading' && (
        <div className="flex items-center gap-2">
          <div className="spinner-small">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <div className="text-xs text-theme-foreground-muted">
            模型加载中...
          </div>
        </div>
      )}
      
      {/* 为其他状态显示进度条 */}
      {aiState.progress !== undefined && aiState.status !== 'loading' && (
        <div className="w-32">
          <div className="w-full bg-theme-background-tertiary rounded-full h-1.5">
            <div 
              className={`h-1.5 rounded-full transition-all duration-300 ${config.color.replace('text-', 'bg-')}`}
              style={{ width: `${aiState.progress}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
}