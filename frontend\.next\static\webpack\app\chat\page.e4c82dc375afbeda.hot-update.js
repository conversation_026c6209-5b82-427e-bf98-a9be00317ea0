"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx":
/*!*******************************************************!*\
  !*** ./src/app/chat/components/AIStatusIndicator.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIStatusIndicator: () => (/* binding */ AIStatusIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,Cog,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,Cog,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,Cog,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Brain,Cog,Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* __next_internal_client_entry_do_not_use__ AIStatusIndicator auto */ \n\n\nfunction AIStatusIndicator(param) {\n    let { aiState } = param;\n    if (aiState.status === 'idle') {\n        return null;\n    }\n    const getStatusConfig = ()=>{\n        switch(aiState.status){\n            case 'loading':\n                return {\n                    icon: _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                    text: aiState.message || '正在加载模型...',\n                    color: 'text-orange-600',\n                    bgColor: 'bg-orange-50',\n                    borderColor: 'border-orange-200'\n                };\n            case 'generating':\n                return {\n                    icon: _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                    text: '正在生成回复...',\n                    color: 'text-blue-600',\n                    bgColor: 'bg-blue-50',\n                    borderColor: 'border-blue-200'\n                };\n            case 'tool_calling':\n                return {\n                    icon: _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    text: aiState.toolName ? \"正在调用 \".concat(aiState.toolName, \"...\") : '正在调用工具...',\n                    color: 'text-green-600',\n                    bgColor: 'bg-green-50',\n                    borderColor: 'border-green-200'\n                };\n            case 'thinking':\n                return {\n                    icon: _barrel_optimize_names_Bot_Brain_Cog_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    text: '正在思考中...',\n                    color: 'text-purple-600',\n                    bgColor: 'bg-purple-50',\n                    borderColor: 'border-purple-200'\n                };\n            default:\n                return null;\n        }\n    };\n    const config = getStatusConfig();\n    if (!config) {\n        return null;\n    }\n    const Icon = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center\",\n        children: [\n            aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner-small\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-theme-foreground-muted\",\n                        children: \"模型加载中...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this),\n            aiState.progress !== undefined && aiState.status !== 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-1.5 rounded-full transition-all duration-300 \".concat(config.color.replace('text-', 'bg-')),\n                        style: {\n                            width: \"\".concat(aiState.progress, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\AIStatusIndicator.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c = AIStatusIndicator;\nvar _c;\n$RefreshReg$(_c, \"AIStatusIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\n"));

/***/ })

});