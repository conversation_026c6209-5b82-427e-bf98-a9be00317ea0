// 主题预加载脚本组件
// 这个组件会在页面加载前立即执行，防止主题闪烁

export function ThemeScript() {
  const themeScript = `
    (function() {
      try {
        // 获取保存的主题或系统主题
        const savedTheme = localStorage.getItem('theme');
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        const theme = savedTheme || systemTheme;
        
        // 立即应用主题类到html元素
        document.documentElement.classList.remove('light', 'dark');
        document.documentElement.classList.add(theme);
        
        // 定义主题颜色
        const themeColors = {
          light: {
            background: '#ffffff',
            backgroundSecondary: '#f8fafc',
            backgroundTertiary: '#f1f5f9',
            foreground: '#0f172a',
            foregroundSecondary: '#334155',
            foregroundMuted: '#64748b',
            border: '#e2e8f0',
            borderSecondary: '#cbd5e1',
            card: '#ffffff',
            cardHover: '#f8fafc',
            input: '#ffffff',
            inputBorder: '#d1d5db',
            inputFocus: '#3b82f6',
            primary: '#3b82f6',
            primaryHover: '#2563eb',
            secondary: '#6b7280',
            secondaryHover: '#4b5563',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444',
            info: '#3b82f6',
            accent: '#8b5cf6',
            accentHover: '#7c3aed',
          },
          dark: {
            background: '#0f172a',
            backgroundSecondary: '#1e293b',
            backgroundTertiary: '#334155',
            foreground: '#f8fafc',
            foregroundSecondary: '#e2e8f0',
            foregroundMuted: '#94a3b8',
            border: '#334155',
            borderSecondary: '#475569',
            card: '#1e293b',
            cardHover: '#334155',
            input: '#1e293b',
            inputBorder: '#475569',
            inputFocus: '#3b82f6',
            primary: '#3b82f6',
            primaryHover: '#2563eb',
            secondary: '#6b7280',
            secondaryHover: '#9ca3af',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444',
            info: '#3b82f6',
            accent: '#8b5cf6',
            accentHover: '#7c3aed',
          }
        };
        
        // 设置CSS变量
        const colors = themeColors[theme];
        const root = document.documentElement;

        Object.entries(colors).forEach(([key, value]) => {
          root.style.setProperty('--color-' + key, value);
        });
        
        // 如果没有保存的主题，保存当前主题
        if (!savedTheme) {
          localStorage.setItem('theme', theme);
        }
      } catch (e) {
        // 如果出错，使用默认浅色主题
        document.documentElement.classList.add('light');
      }
    })();
  `;

  return (
    <script
      dangerouslySetInnerHTML={{
        __html: themeScript,
      }}
    />
  );
}
