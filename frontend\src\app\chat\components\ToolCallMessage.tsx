import React from 'react';
import { Wrench, ChevronDown, ChevronRight, Loader2, Check, AlertCircle } from 'lucide-react';
import { useState } from 'react';

interface ToolCallMessageProps {
  toolCall: {
    id: string;
    toolName: string;
    args: any;
    status: 'executing' | 'completed' | 'error';
    result?: string;
    error?: string;
    startTime: number;
    executionTime?: number;
  };
}

export function ToolCallMessage({ toolCall }: ToolCallMessageProps) {
  const [isExpanded, setIsExpanded] = useState(false);



  const getStatusColor = () => {
    switch (toolCall.status) {
      case 'executing':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 border-blue-300 dark:border-blue-700';
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 border-green-300 dark:border-green-700';
      case 'error':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200 border-red-300 dark:border-red-700';
      default:
        return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-700';
    }
  };



  // 检测并渲染图片URL
  const renderImageIfUrl = (text: string | any) => {
    // 确保text是字符串类型
    const textStr = typeof text === 'string' ? text : JSON.stringify(text, null, 2);
    const imageUrlRegex = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp|svg))/gi;
    const parts = textStr.split(imageUrlRegex);
    
    if (parts.length === 1) {
      return <span>{textStr}</span>;
    }
    
    return (
      <div className="space-y-2">
        {parts.map((part, index) => {
          if (imageUrlRegex.test(part)) {
            return (
              <div key={index} className="my-2">
                <img 
                  src={part} 
                  alt="Generated image" 
                  className="max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <div className="text-xs text-gray-500 mt-1">{part}</div>
              </div>
            );
          }
          return part ? <span key={index}>{part}</span> : null;
        })}
      </div>
    );
  };

  const renderResult = () => {
    if (toolCall.status === 'executing') {
      return null;
    }

    if (toolCall.status === 'error') {
      return (
        <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded">
          <div className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">错误信息：</div>
          <div className="text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap">
            {toolCall.error || '未知错误'}
          </div>
        </div>
      );
    }

    if (toolCall.status === 'completed' && toolCall.result) {
      // 尝试解析JSON格式的结果
      let formattedResult = toolCall.result;
      let isJsonResult = false;
      
      try {
        const parsed = JSON.parse(toolCall.result);
        formattedResult = JSON.stringify(parsed, null, 2);
        isJsonResult = true;
      } catch (e) {
        // 不是JSON格式，保持原样
      }
      
      return (
        <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded">
          <div className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-1">执行结果：</div>
          <div className="text-sm text-gray-700 dark:text-gray-300 max-h-96 overflow-y-auto">
            {isJsonResult ? (
              <pre className="whitespace-pre-wrap font-mono text-xs bg-black/5 dark:bg-white/5 p-2 rounded">
                {formattedResult}
              </pre>
            ) : (
              <div className="whitespace-pre-wrap">
                {renderImageIfUrl(formattedResult)}
              </div>
            )}
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="flex gap-3 justify-start">
      <div className="flex flex-col items-start">
        <div className="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 bg-orange-600">
          <Wrench className="w-4 h-4 text-white" />
        </div>
      </div>
      
      <div className="flex flex-col max-w-[70%] min-w-[300px]">
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs text-gray-500 dark:text-gray-400">工具调用</span>
        </div>
        
        <div className={`rounded-lg border ${getStatusColor()}`}>
          {/* 折叠状态的头部 */}
          <div 
            className="px-4 py-3 cursor-pointer hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Wrench className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                <span className="text-sm font-medium">{toolCall.toolName}</span>
              </div>
              <div className="flex items-center gap-2">
                {/* 状态图标显示 */}
                <div className="flex items-center">
                  {toolCall.status === 'executing' ? (
                    <Loader2 className="w-4 h-4 text-orange-600 dark:text-orange-400 animate-spin" />
                  ) : toolCall.status === 'completed' ? (
                    <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                  )}
                </div>
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-400" />
                )}
              </div>
            </div>
          </div>
          
          {/* 展开状态的详细内容 */}
          {isExpanded && (
            <div className="px-4 pb-3 border-t border-gray-200 dark:border-gray-700">
              <div className="space-y-3 pt-3">
                <div>
                  <div className="text-xs opacity-75">
                    <strong>参数：</strong>
                    <pre className="mt-1 text-xs bg-black/5 dark:bg-white/5 p-2 rounded overflow-x-auto">
                      {JSON.stringify(toolCall.args, null, 2)}
                    </pre>
                  </div>
                </div>
                
                {renderResult()}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}