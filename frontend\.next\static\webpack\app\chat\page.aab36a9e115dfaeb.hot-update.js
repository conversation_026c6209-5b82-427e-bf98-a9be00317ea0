"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/MessageList.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageList.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageList: () => (/* binding */ MessageList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_chat_components_MessageItem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/chat/components/MessageItem */ \"(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\");\n/* harmony import */ var _app_chat_components_ToolCallMessage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/chat/components/ToolCallMessage */ \"(app-pages-browser)/./src/app/chat/components/ToolCallMessage.tsx\");\n/* harmony import */ var _app_chat_components_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/chat/components/AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageList auto */ \nvar _s = $RefreshSig$();\n\n// 从 MessageItem 组件文件导入 MessageItem\n\n\n\nfunction MessageList(param) {\n    let { messages, isStreaming, aiState, activeToolCalls, toolCallMessages, thinkingStartTime } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MessageList.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"MessageList.useEffect\"], [\n        messages,\n        isStreaming\n    ]);\n    // 合并消息和工具调用消息，按时间排序\n    const allItems = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"MessageList.useMemo[allItems]\": ()=>{\n            // 过滤掉旧的tool role消息，因为现在使用tool_call role\n            const filteredMessages = messages.filter({\n                \"MessageList.useMemo[allItems].filteredMessages\": (message)=>{\n                    return message.role !== 'tool';\n                }\n            }[\"MessageList.useMemo[allItems].filteredMessages\"]);\n            // 创建消息项\n            const messageItems = filteredMessages.sort({\n                \"MessageList.useMemo[allItems].messageItems\": (a, b)=>a.id - b.id\n            }[\"MessageList.useMemo[allItems].messageItems\"]).map({\n                \"MessageList.useMemo[allItems].messageItems\": (message)=>({\n                        type: 'message',\n                        data: message,\n                        id: message.id,\n                        sortId: message.id\n                    })\n            }[\"MessageList.useMemo[allItems].messageItems\"]);\n            // 创建工具调用项\n            const toolCallItems = toolCallMessages.map({\n                \"MessageList.useMemo[allItems].toolCallItems\": (toolCall)=>({\n                        type: 'tool_call',\n                        data: toolCall,\n                        id: \"tool_call_\".concat(toolCall.id),\n                        sortId: parseInt(toolCall.id)\n                    })\n            }[\"MessageList.useMemo[allItems].toolCallItems\"]);\n            // 合并并按数据库ID排序（确保正确的时间顺序）\n            return [\n                ...messageItems,\n                ...toolCallItems\n            ].sort({\n                \"MessageList.useMemo[allItems]\": (a, b)=>a.sortId - b.sortId\n            }[\"MessageList.useMemo[allItems]\"]);\n        }\n    }[\"MessageList.useMemo[allItems]\"], [\n        messages,\n        toolCallMessages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 space-y-4 max-h-full bg-theme-background transition-colors duration-300\",\n        children: [\n            allItems.map((item, index)=>{\n                if (item.type === 'message') {\n                    const message = item.data;\n                    // 过滤掉工具消息，因为工具调用现在由ToolCallMessage组件处理\n                    if (message.role === 'tool') {\n                        return null;\n                    }\n                    const isLastAssistantMessage = message.role === 'assistant' && index === allItems.length - 1 && aiState && aiState.status !== 'idle';\n                    // 计算消息在对话中的索引（只计算助手消息）\n                    const assistantMessages = messages.filter((msg)=>msg.role === 'assistant');\n                    const messageIndex = message.role === 'assistant' ? assistantMessages.findIndex((msg)=>msg.id === message.id) : -1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_MessageItem__WEBPACK_IMPORTED_MODULE_2__.MessageItem, {\n                        message: message,\n                        messageIndex: messageIndex >= 0 ? messageIndex : undefined,\n                        showAIStatus: isLastAssistantMessage,\n                        aiState: isLastAssistantMessage ? aiState : undefined,\n                        activeToolCalls: activeToolCalls,\n                        thinkingStartTime: isLastAssistantMessage ? thinkingStartTime || undefined : undefined\n                    }, message.id || \"message-\".concat(index), false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageList.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, this);\n                } else if (item.type === 'tool_call') {\n                    const toolCall = item.data;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_ToolCallMessage__WEBPACK_IMPORTED_MODULE_3__.ToolCallMessage, {\n                        toolCall: toolCall\n                    }, item.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageList.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, this);\n                }\n                return null;\n            }),\n            (isStreaming && !messages.some((msg)=>msg.role === 'assistant') || aiState && aiState.status !== 'idle' && aiState.status !== 'tool_calling' && (!messages.some((msg)=>msg.role === 'assistant') || aiState.status === 'loading')) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_chat_components_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_4__.AIStatusIndicator, {\n                aiState: aiState || {\n                    status: 'loading',\n                    message: '正在准备...'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageList.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: messagesEndRef\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageList.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageList.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageList, \"dAwZQJDcVqJdF/2irgmHmdCxjwA=\");\n_c = MessageList;\nvar _c;\n$RefreshReg$(_c, \"MessageList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageList.tsx\n"));

/***/ })

});