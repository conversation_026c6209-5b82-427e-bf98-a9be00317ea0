'use client';

import React, { useEffect, useRef } from 'react';
// 从 MessageItem 组件文件导入 MessageItem
import { MessageItem } from '@/app/chat/components/MessageItem';
import { ToolCallMessage } from '@/app/chat/components/ToolCallMessage';

import { AIStatusIndicator } from '@/app/chat/components/AIStatusIndicator';
import { Message } from '@/lib/database';
import { AIState } from '../types';

interface MessageListProps {
  messages: Message[];
  isStreaming: boolean;
  aiState?: AIState;
  activeToolCalls?: Map<string, any>;
  toolCallMessages: any[]; // 保留兼容性，但不再使用
  thinkingStartTime?: number | null;
}

export function MessageList({ messages, isStreaming, aiState, activeToolCalls, toolCallMessages, thinkingStartTime }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isStreaming]);

  // 合并消息和工具调用消息，按时间排序
  const allItems = React.useMemo(() => {
    // 过滤掉旧的tool role消息，因为现在使用tool_call role
    const filteredMessages = messages.filter(message => {
      return message.role !== 'tool';
    });

    // 创建消息项
    const messageItems = filteredMessages
      .sort((a, b) => a.id - b.id)
      .map(message => ({
        type: 'message' as const,
        data: message,
        id: message.id,
        sortId: message.id
      }));

    // 创建工具调用项
    const toolCallItems = toolCallMessages.map(toolCall => ({
      type: 'tool_call' as const,
      data: toolCall,
      id: `tool_call_${toolCall.id}`,
      sortId: parseInt(toolCall.id)
    }));

    // 合并并按数据库ID排序（确保正确的时间顺序）
    return [...messageItems, ...toolCallItems]
      .sort((a, b) => a.sortId - b.sortId);
  }, [messages, toolCallMessages]);

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4 max-h-full bg-theme-background transition-colors duration-300">
      {allItems.map((item, index) => {
        if (item.type === 'message') {
          const message = item.data as Message;
          // 过滤掉工具消息，因为工具调用现在由ToolCallMessage组件处理
          if (message.role === 'tool') {
            return null;
          }
          const isLastAssistantMessage = message.role === 'assistant' &&
            index === allItems.length - 1 &&
            aiState && aiState.status !== 'idle';

          // 计算消息在对话中的索引（只计算助手消息）
          const assistantMessages = messages.filter(msg => msg.role === 'assistant');
          const messageIndex = message.role === 'assistant'
            ? assistantMessages.findIndex(msg => msg.id === message.id)
            : -1;

          return (
            <MessageItem
              key={message.id || `message-${index}`}
              message={message}
              messageIndex={messageIndex >= 0 ? messageIndex : undefined}
              showAIStatus={isLastAssistantMessage}
              aiState={isLastAssistantMessage ? aiState : undefined}
              activeToolCalls={activeToolCalls}
              thinkingStartTime={isLastAssistantMessage ? thinkingStartTime || undefined : undefined}
            />
          );
        } else if (item.type === 'tool_call') {
          const toolCall = item.data;
          return (
            <ToolCallMessage
              key={item.id}
              toolCall={toolCall}
            />
          );
        }
        return null;
      })}
      
      {/* 显示独立的状态指示器的条件：
          1. 正在流式传输且没有助手消息
          2. 有AI状态且状态不是idle（包括loading、generating等状态）
          3. 特别处理：如果是loading状态，即使有助手消息也要显示
          4. 工具调用状态不在这里显示，而是在消息气泡内显示 */}
      {((isStreaming && !messages.some(msg => msg.role === 'assistant')) || 
        (aiState && aiState.status !== 'idle' && aiState.status !== 'tool_calling' &&
         (!messages.some(msg => msg.role === 'assistant') || aiState.status === 'loading'))) && (
        <AIStatusIndicator aiState={aiState || { status: 'loading', message: '正在准备...' }} />
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}