// 主题类型定义
export type Theme = 'light' | 'dark';

// 主题配置接口
export interface ThemeConfig {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

// 主题颜色方案
export const themeColors = {
  light: {
    // 背景色
    background: '#ffffff',
    backgroundSecondary: '#f8fafc',
    backgroundTertiary: '#f1f5f9',
    
    // 前景色
    foreground: '#0f172a',
    foregroundSecondary: '#334155',
    foregroundMuted: '#64748b',
    
    // 边框色
    border: '#e2e8f0',
    borderSecondary: '#cbd5e1',
    
    // 卡片背景
    card: '#ffffff',
    cardHover: '#f8fafc',
    
    // 输入框
    input: '#ffffff',
    inputBorder: '#d1d5db',
    inputFocus: '#3b82f6',
    
    // 按钮
    primary: '#3b82f6',
    primaryHover: '#2563eb',
    secondary: '#6b7280',
    secondaryHover: '#4b5563',
    
    // 状态色
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    
    // 特殊色
    accent: '#8b5cf6',
    accentHover: '#7c3aed',
  },
  dark: {
    // 背景色
    background: '#0f172a',
    backgroundSecondary: '#1e293b',
    backgroundTertiary: '#334155',
    
    // 前景色
    foreground: '#f8fafc',
    foregroundSecondary: '#e2e8f0',
    foregroundMuted: '#94a3b8',
    
    // 边框色
    border: '#334155',
    borderSecondary: '#475569',
    
    // 卡片背景
    card: '#1e293b',
    cardHover: '#334155',
    
    // 输入框
    input: '#1e293b',
    inputBorder: '#475569',
    inputFocus: '#3b82f6',
    
    // 按钮
    primary: '#3b82f6',
    primaryHover: '#2563eb',
    secondary: '#6b7280',
    secondaryHover: '#9ca3af',
    
    // 状态色
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    
    // 特殊色
    accent: '#8b5cf6',
    accentHover: '#7c3aed',
  }
};

// 获取系统主题偏好
export const getSystemTheme = (): Theme => {
  if (typeof window !== 'undefined') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
};

// 从localStorage获取保存的主题
export const getSavedTheme = (): Theme | null => {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('theme');
    if (saved === 'light' || saved === 'dark') {
      return saved;
    }
  }
  return null;
};

// 保存主题到localStorage
export const saveTheme = (theme: Theme): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('theme', theme);
  }
};

// 应用主题到DOM（带过渡效果）
export const applyTheme = (theme: Theme): void => {
  if (typeof window !== 'undefined') {
    const root = document.documentElement;
    const colors = themeColors[theme];

    // 添加过渡类
    root.classList.add('theme-changing');

    // 移除旧的主题类
    root.classList.remove('light', 'dark');
    // 添加新的主题类
    root.classList.add(theme);

    // 设置CSS变量
    Object.entries(colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // 移除过渡类（在过渡完成后）
    setTimeout(() => {
      root.classList.remove('theme-changing');
    }, 300);
  }
};

// 初始化主题
export const initializeTheme = (): Theme => {
  const savedTheme = getSavedTheme();
  const theme = savedTheme || getSystemTheme();
  applyTheme(theme);
  return theme;
};
