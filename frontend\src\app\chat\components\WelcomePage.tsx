'use client';

import React from 'react';
import { Bot, Plus } from 'lucide-react';
import { OllamaModel } from '../types';

interface WelcomePageProps {
  models: OllamaModel[];
  selectedModel: string;
  error: string | null;
  onCreateConversation: () => void;
}

export function WelcomePage({
  models,
  selectedModel,
  error,
  onCreateConversation,
}: WelcomePageProps) {
  return (
    <div className="flex-1 flex items-center justify-center">
      <div className="text-center">
        <Bot className="w-16 h-16 text-theme-foreground-muted mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-theme-foreground mb-2">
          Kun Agent
        </h2>
        <p className="text-theme-foreground-muted mb-6">
          选择一个模型并创建新对话开始聊天
        </p>

        {error && (
          <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg text-red-700 dark:text-red-300 text-sm max-w-md mx-auto">
            {error}
          </div>
        )}

        {models.length === 0 ? (
          <div className="text-theme-foreground-muted">
            <p>正在加载模型...</p>
            <p className="text-sm mt-2">请确保 Ollama 正在运行</p>
          </div>
        ) : (
          <button
            onClick={onCreateConversation}
            disabled={!selectedModel}
            className="px-6 py-3 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 mx-auto transition-colors duration-200"
          >
            <Plus className="w-5 h-5" />
            创建新对话
          </button>
        )}
      </div>
    </div>
  );
}