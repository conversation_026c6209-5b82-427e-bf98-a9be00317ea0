import React from 'react';
import { ArrowLeft, Plus } from 'lucide-react';
import Link from 'next/link';

interface PageHeaderProps {
  onAddServer: () => void;
}

export function PageHeader({
  onAddServer
}: PageHeaderProps) {
  return (
    <div className="bg-theme-card border-b border-theme-border transition-colors duration-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-4">
            <Link href="/" className="p-2 text-theme-foreground-muted hover:text-theme-foreground transition-colors duration-200">
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <h1 className="text-xl font-bold text-theme-foreground">
              MCP 服务器配置
            </h1>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={onAddServer}
              className="flex items-center gap-2 px-4 py-2 bg-theme-primary text-white rounded-lg hover:bg-theme-primary-hover transition-colors duration-200"
            >
              <Plus className="w-4 h-4" />
              添加服务器
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}